# 开方界面用量支持小数功能说明

## 功能概述

本次修改为开方界面的用法用量部分增加了小数支持功能。针对**水丸、蜜丸、膏方**这三种剂型，在点击"选择用量"弹窗中的"自定义"按钮时，弹出的自定义用量弹窗现在支持输入1位小数（如0.5、1.2等）。

**注意：胶囊剂型依然只支持整数输入，保持原有逻辑不变。**

## 修改内容

### 1. 布局文件修改

#### 新增胶囊专用布局文件
- **文件路径**: `app/src/main/res/layout/custom_capsule_dosage_dialog_input.xml`
- **用途**: 专门用于胶囊剂型的自定义用量弹窗，保持只支持整数输入
- **输入类型**: `android:inputType="number"`（只允许整数）
- **最大长度**: `android:maxLength="3"`

#### 修改原有布局文件
- **文件路径**: `app/src/main/res/layout/custom_pill_dosage_dialog_input.xml`
- **用途**: 用于水丸、蜜丸、膏方的自定义用量弹窗，支持小数输入
- **输入类型**: `android:inputType="numberDecimal"`（支持小数）
- **最大长度**: `android:maxLength="4"`（支持如"12.5"这样的输入）

### 2. Java代码修改

#### 新增变量
在 `MedicationFragment.java` 中新增了支持小数的变量：
```java
private double selectedUnitCountDecimal = 1.0; // 蜜丸单位数量（支持小数）
private double selectedWaterPillUnitCountDecimal = 1.0; // 水丸单位数量（支持小数）
private double selectedCreamFormulaUnitCountDecimal = 1.0; // 膏方单位数量（支持小数）
```

#### 新增辅助方法
1. **`isValidDecimalInput(String input)`**: 验证小数输入是否有效（支持1位小数）
2. **`formatDecimalValue(double value)`**: 格式化小数值显示（去除不必要的小数点和0）
3. **`updatePillGramDisplayWithDecimal(double unitCount)`**: 使用小数值更新蜜丸克数显示
4. **`updateWaterPillGramDisplayWithDecimal(double unitCount)`**: 使用小数值更新水丸克数显示
5. **`updateCreamFormulaGramDisplayWithDecimal(double unitCount)`**: 使用小数值更新膏方克数显示

#### 修改自定义用量弹窗逻辑
- **蜜丸**: `showCustomPillDosageDialog()` 方法中的输入验证逻辑
- **水丸**: `showCustomWaterPillDosageDialog()` 方法中的输入验证逻辑
- **膏方**: `showCustomCreamFormulaDosageDialog()` 方法中的输入验证逻辑
- **胶囊**: `showCustomCapsuleDosageDialog()` 方法使用新的布局文件，保持原有整数验证逻辑

## 功能特性

### 支持的输入格式
- **整数**: 1, 2, 3, 10, 99 等
- **一位小数**: 0.5, 1.2, 2.7, 10.5 等
- **不支持**: 多位小数（如1.25）、负数、非数字字符

### 输入验证
- 使用正则表达式 `^\\d+(\\.\\d{1})?$` 验证输入格式
- 确保输入值大于0
- 提供友好的错误提示信息

### 显示优化
- 整数值不显示小数点（如输入"1.0"显示为"1"）
- 小数值保留1位小数（如输入"1.5"显示为"1.5"）
- 克数计算精确到1位小数

## 剂型区分

| 剂型 | 支持小数 | 布局文件 | 输入类型 |
|------|----------|----------|----------|
| 蜜丸 | ✅ | custom_pill_dosage_dialog_input.xml | numberDecimal |
| 水丸 | ✅ | custom_pill_dosage_dialog_input.xml | numberDecimal |
| 膏方 | ✅ | custom_pill_dosage_dialog_input.xml | numberDecimal |
| 胶囊 | ❌ | custom_capsule_dosage_dialog_input.xml | number |

## 使用流程

1. 用户在开方界面选择水丸、蜜丸或膏方剂型
2. 点击用法用量部分的"选择用量"
3. 在弹出的选择弹窗中点击"自定义"按钮
4. 在自定义用量弹窗中输入数值（支持1位小数）
5. 点击"保存"按钮完成设置
6. 系统自动计算并显示对应的克数

## 兼容性说明

- 保持与现有代码的兼容性
- 原有的整数变量仍然保留，用于兼容其他模块
- 新增的小数变量用于精确计算和显示
- 胶囊剂型的逻辑完全不受影响

## 测试建议

### 功能测试
1. 测试水丸、蜜丸、膏方的小数输入功能
2. 测试胶囊剂型仍然只支持整数
3. 测试各种边界值输入（0.1, 0.9, 99.9等）
4. 测试无效输入的错误提示

### 界面测试
1. 验证小数显示格式正确
2. 验证克数计算准确
3. 验证弹窗布局正常显示
4. 验证键盘输入类型正确

### 兼容性测试
1. 测试与其他功能模块的兼容性
2. 测试数据保存和恢复功能
3. 测试不同剂型之间的切换

## 实现细节

### 数据存储策略
为了保持与现有代码的兼容性，采用了双变量存储策略：
- **整数变量**：`selectedUnitCount`、`selectedWaterPillUnitCount`、`selectedCreamFormulaUnitCount` - 用于兼容现有逻辑
- **小数变量**：`selectedUnitCountDecimal`、`selectedWaterPillUnitCountDecimal`、`selectedCreamFormulaUnitCountDecimal` - 用于精确计算和显示

### 输入验证正则表达式
```java
String pattern = "^\\d+(\\.\\d{1})?$";
```
- `^\\d+` - 匹配一个或多个数字开头
- `(\\.\\d{1})?` - 可选的小数部分，最多1位小数
- `$` - 字符串结尾

### 格式化显示逻辑
```java
private String formatDecimalValue(double value) {
    if (value == Math.floor(value)) {
        return String.valueOf((int) value); // 整数不显示小数点
    } else {
        return String.format("%.1f", value); // 保留1位小数
    }
}
```

### 克数计算
使用小数值进行精确计算：
```java
double totalGram = selectedUnitCountDecimal * currentSpecGramPerUnit;
```

## 注意事项

### 1. 布局文件分离
- 胶囊剂型使用独立的布局文件 `custom_capsule_dosage_dialog_input.xml`
- 水丸、蜜丸、膏方共享 `custom_pill_dosage_dialog_input.xml`
- 确保胶囊剂型的输入限制不受影响

### 2. 数据缓存和恢复
- 缓存时使用格式化后的小数值字符串
- 恢复时优先解析为小数，然后四舍五入为整数用于兼容
- 确保缓存数据的向前兼容性

### 3. 错误处理
- 输入验证失败时提供明确的错误提示
- 数据解析异常时使用合理的默认值
- 避免因小数处理导致的崩溃

### 4. 性能考虑
- 小数计算使用 `Double.parseDouble()` 而非 `BigDecimal`
- 格式化显示时避免不必要的字符串操作
- 正则表达式验证效率较高

## 后续优化建议

1. **扩展支持**：如果需要支持更多位小数，只需修改正则表达式和格式化逻辑
2. **国际化**：考虑不同地区的小数点表示方式
3. **用户体验**：可以考虑添加小数输入的键盘优化
4. **数据迁移**：如果需要完全迁移到小数存储，可以逐步替换整数变量
