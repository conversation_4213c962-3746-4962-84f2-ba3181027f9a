# 开方界面小数用量功能测试用例

## 测试环境准备

### 前置条件
1. 确保应用已集成最新的代码修改
2. 准备测试用的水丸、蜜丸、膏方、胶囊剂型数据
3. 确保有规格数据可供选择

## 功能测试用例

### 测试用例1：水丸剂型小数输入
**测试步骤：**
1. 进入开方界面
2. 选择水丸剂型
3. 点击"选择用量"弹窗
4. 点击"自定义"按钮
5. 输入小数值（如1.5）
6. 点击"保存"

**预期结果：**
- 弹窗接受1.5的输入
- 显示为"1.5丸"
- 克数正确计算并显示
- 数据能正确保存

**测试数据：**
- 有效输入：0.5, 1.2, 2.7, 10.5
- 无效输入：1.25, -1.5, abc, 空值

### 测试用例2：蜜丸剂型小数输入
**测试步骤：**
1. 进入开方界面
2. 选择蜜丸剂型
3. 点击"选择用量"弹窗
4. 点击"自定义"按钮
5. 输入小数值（如0.5）
6. 点击"保存"

**预期结果：**
- 弹窗接受0.5的输入
- 显示为"0.5丸"
- 克数正确计算并显示
- 数据能正确保存

### 测试用例3：膏方剂型小数输入
**测试步骤：**
1. 进入开方界面
2. 选择膏方剂型
3. 选择非"瓶"单位的规格（如"勺"）
4. 点击"选择用量"弹窗
5. 点击"自定义"按钮
6. 输入小数值（如1.5）
7. 点击"保存"

**预期结果：**
- 弹窗接受1.5的输入
- 显示为"1.5勺"
- 克数正确计算并显示
- 数据能正确保存

### 测试用例4：胶囊剂型整数限制
**测试步骤：**
1. 进入开方界面
2. 选择胶囊剂型
3. 点击"选择用量"弹窗
4. 点击"自定义"按钮
5. 尝试输入小数值（如1.5）
6. 点击"保存"

**预期结果：**
- 输入框不允许输入小数点
- 或者输入小数后提示错误
- 只能输入整数值
- 保持原有的整数限制逻辑

### 测试用例5：输入验证测试
**测试数据：**

| 输入值 | 预期结果 | 说明 |
|--------|----------|------|
| 1 | 接受 | 整数 |
| 1.5 | 接受 | 一位小数 |
| 0.5 | 接受 | 小于1的小数 |
| 10.9 | 接受 | 两位数小数 |
| 1.25 | 拒绝 | 两位小数 |
| -1.5 | 拒绝 | 负数 |
| 0 | 拒绝 | 零值 |
| abc | 拒绝 | 非数字 |
| 空值 | 拒绝 | 空输入 |
| 1. | 拒绝 | 不完整小数 |

### 测试用例6：数据缓存和恢复
**测试步骤：**
1. 设置水丸用量为1.5
2. 退出开方界面
3. 重新进入开方界面
4. 检查用量显示

**预期结果：**
- 用量正确恢复为1.5
- 克数计算正确
- 显示格式正确

### 测试用例7：剂型切换测试
**测试步骤：**
1. 在水丸剂型设置用量为1.5
2. 切换到胶囊剂型
3. 再切换回水丸剂型

**预期结果：**
- 切换到胶囊时，用量重置或使用默认值
- 切换回水丸时，如果有缓存则恢复1.5
- 不同剂型之间不会相互影响

## 界面测试用例

### 测试用例8：显示格式测试
**测试数据：**

| 输入值 | 预期显示 | 说明 |
|--------|----------|------|
| 1.0 | 1丸 | 整数不显示小数点 |
| 1.5 | 1.5丸 | 小数正常显示 |
| 2.0 | 2丸 | 整数不显示小数点 |
| 0.5 | 0.5丸 | 小于1的小数 |

### 测试用例9：克数计算测试
**假设规格为3g/丸：**

| 输入数量 | 预期克数显示 | 计算公式 |
|----------|--------------|----------|
| 1 | (3g) | 1 × 3 = 3 |
| 1.5 | (4.5g) | 1.5 × 3 = 4.5 |
| 0.5 | (1.5g) | 0.5 × 3 = 1.5 |
| 2.5 | (7.5g) | 2.5 × 3 = 7.5 |

## 边界测试用例

### 测试用例10：最大值测试
**测试步骤：**
1. 输入最大允许的值（如99.9）
2. 检查是否正确处理

**预期结果：**
- 能正确接受和显示
- 克数计算正确
- 不会导致溢出或错误

### 测试用例11：最小值测试
**测试步骤：**
1. 输入最小允许的值（如0.1）
2. 检查是否正确处理

**预期结果：**
- 能正确接受和显示
- 克数计算正确
- 显示格式正确

## 兼容性测试用例

### 测试用例12：向前兼容性
**测试步骤：**
1. 使用旧版本创建的缓存数据
2. 升级到新版本
3. 检查数据是否正确加载

**预期结果：**
- 旧的整数数据能正确显示
- 不会导致崩溃或错误
- 功能正常工作

### 测试用例13：向后兼容性
**测试步骤：**
1. 使用新版本创建小数用量数据
2. 检查在不支持小数的模块中的表现

**预期结果：**
- 不会导致其他模块崩溃
- 数据能合理降级处理
- 核心功能不受影响

## 性能测试用例

### 测试用例14：响应速度测试
**测试步骤：**
1. 快速连续输入和修改用量
2. 测试弹窗打开和关闭速度
3. 测试计算和显示更新速度

**预期结果：**
- 响应速度不低于原有功能
- 无明显延迟或卡顿
- 内存使用合理

## 错误处理测试用例

### 测试用例15：异常输入处理
**测试步骤：**
1. 输入各种异常值
2. 检查错误提示是否友好
3. 检查是否能正确恢复

**预期结果：**
- 错误提示清晰明确
- 不会导致应用崩溃
- 能正确恢复到可用状态

## 测试报告模板

### 测试结果记录
- **测试用例编号**：
- **测试时间**：
- **测试环境**：
- **测试结果**：通过/失败
- **实际结果**：
- **问题描述**：
- **截图**：

### 问题分类
- **严重问题**：导致功能无法使用或应用崩溃
- **一般问题**：功能可用但体验不佳
- **轻微问题**：界面显示或提示不够完善
- **建议优化**：可以进一步改进的地方
