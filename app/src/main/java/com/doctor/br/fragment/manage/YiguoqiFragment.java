package com.doctor.br.fragment.manage;

import android.graphics.drawable.Drawable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ImageSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.activity.manage.OrderActivity;
import com.doctor.br.adapter.RecyclerItemItemClickListener;
import com.doctor.br.adapter.manage.YiguoqiRecylerAdapter;
import com.doctor.br.bean.YiguoqiOrderListBean;
import com.doctor.br.bean.event.OrderListVisibleEvent;
import com.doctor.br.bean.event.RefreshOrderEvent;
import com.doctor.br.bean.event.SelectOrderEvent;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.UIHelper;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.fragment.BaseFragment;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.EmptyView;
import org.newapp.ones.base.widgets.LoadingDialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bingoogolapple.refreshlayout.BGANormalRefreshViewHolder;
import cn.bingoogolapple.refreshlayout.BGARefreshLayout;

/**
 * 类描述：已过期订单
 * 创建人：ShiShaoPo
 * 创建时间：2017/10/19 9:54
 * 修改人：ShiShaoPo
 * 修改时间：2017/10/19 9:54
 */

public class YiguoqiFragment extends BaseFragment implements RecyclerItemItemClickListener {
    //界面下的控件
    @BindView(R.id.hint_linear)
    LinearLayout hintLinear;
    @BindView(R.id.close_img)
    ImageView closeImg;
    @BindView(R.id.bga_refreshLayout)
    BGARefreshLayout bgaRefreshLayout;
    @BindView(R.id.yiguoqi_recycler)
    RecyclerView yiguoqiRecycler;
    @BindView(R.id.empty_view)
    EmptyView emptyView;
    @BindView(R.id.gone_tv)
    TextView goneTv;

    private boolean isShow;//是否显示订单

    private RequestCallBack getOrderListCallBack;//网络请求获取已过期订单列表回调
    private RequestCallBack reUseOrderCallBack;//网络请求重新启用订单回调

    private int currentPage = 1;//当前页码
    private int totalPage = -1;//总页码

    private List<YiguoqiOrderListBean.ListBean> orderList;//已过期订单列表
    private YiguoqiRecylerAdapter yiguoqiRecylerAdapter;//已过期订单列表适配器

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_yiguoqi;
    }

    @Override
    protected void init() {
        super.init();
        getBundleData();
        EventBusUtils.register(this);
        initView();
        getYiguoqiOrderListRequest("1");
    }

    private void getBundleData() {
        isShow = getArguments().getBoolean(OrderActivity.IS_SHOW, false);
    }

    private void initView() {
        bgaRefreshLayout.setDelegate(new BGARefreshLayout.BGARefreshLayoutDelegate() {
            @Override
            public void onBGARefreshLayoutBeginRefreshing(BGARefreshLayout refreshLayout) {
                mLoadingDialog = null;
                getYiguoqiOrderListRequest("1");
            }

            @Override
            public boolean onBGARefreshLayoutBeginLoadingMore(BGARefreshLayout refreshLayout) {
                if (totalPage != -1) {
                    if (currentPage < totalPage) {
                        mLoadingDialog = null;
                        getYiguoqiOrderListRequest(currentPage + 1 + "");
                        return true;
                    }
                }
                return false;
            }
        });
        BGANormalRefreshViewHolder viewHolder = new BGANormalRefreshViewHolder(getActivity(), true);
        viewHolder.setLoadingMoreText("正在加载更多数据...");
        bgaRefreshLayout.setRefreshViewHolder(viewHolder);

        yiguoqiRecycler.setHasFixedSize(true);
        orderList = new ArrayList<>();
        yiguoqiRecylerAdapter = new YiguoqiRecylerAdapter(getActivity(), orderList, this);
        yiguoqiRecycler.setAdapter(yiguoqiRecylerAdapter);

        closeImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissHint();
            }
        });
    }

    private ConfirmDialog reUseDialog;//重新启用对话框
    private int reUsePosition = -1;//重新启用的位置

    @Override
    public void itemViewClick(final int position, View view) {
        switch (view.getId()) {
            case R.id.remind_btn:
//                if (reUseDialog != null && reUseDialog.isShowing()) {
//                    return;
//                }
//                reUseDialog = ConfirmDialog.getInstance(getActivity())
//                        .setDialogContent("确定重新启用订单？")
//                        .setNavigationText("取消")
//                        .setPositiveText("确定")
//                        .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
//                            @Override
//                            public void onNavigationBtnClicked(View view) {
//                                reUseDialog.dismiss();
//                            }
//
//                            @Override
//                            public void onPositiveBtnClicked(View view) {
//                                reUsePosition = position;
//                                reUseOrderReuqest(position);
//                            }
//                        });
//                reUseDialog.show();
                break;
            case R.id.more_view:
                UIHelper.openMedicationDetailActivity(getActivity(), orderList.get(position).getOrderId());
                break;
            default:
                break;
        }
    }

    private boolean isFragmentVisible;//判断当前fragment是否显示
    private boolean hasData;//判断是否有数据

    /**
     * 更新提示语是否显示
     *
     * @send {@link OrderActivity}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void refreshHintVisible(SelectOrderEvent selectOrderEvent) {
        if (selectOrderEvent.getPosition() == 2) {
            isFragmentVisible = true;
        } else {
            isFragmentVisible = false;
        }
        showHint();
    }

    /**
     * 控制显示第一次进来的提示语
     */
    private void showHint() {
        //是否手动关闭过已过期页面提示，false是未关闭过，true是已关闭过
        boolean isManuallyClosed = SharedPreferenceForeverUtils.getBoolean(getActivity(), PublicParams.IS_FIRST_YIGUOQI);
        if (isFragmentVisible && hasData && !isManuallyClosed) {
            hintLinear.setVisibility(View.VISIBLE);
            // 移除自动消失逻辑，只有手动关闭才会记录状态
        }
    }



    /**
     * 控制隐藏第一次进来的提示语
     */
    private void dismissHint() {
        if (hintLinear != null && hintLinear.getVisibility() == View.VISIBLE) {
            hintLinear.setVisibility(View.GONE);
            // 只有手动关闭时才记录关闭状态
            SharedPreferenceForeverUtils.putBoolean(getActivity(), PublicParams.IS_FIRST_YIGUOQI, true);
        }
    }

    /**
     * 使用EventBus更新列表显示与否状态
     *
     * @sender 发送class
     * {@link com.doctor.br.activity.manage.OrderActivity#refreshOrderListVisibility()}订单页面
     * @receiver 接收class
     * @receiver {@link com.doctor.br.fragment.manage.DaifukuanFragment#refreshOrderListVisible}待付款页面
     * @receiver {@link com.doctor.br.fragment.manage.YiwanchengFragment#refreshOrderListVisible}已完成页面
     * @receiver {@link com.doctor.br.fragment.manage.YiguoqiFragment#refreshOrderListVisible}已过期页面
     * @receiver {@link com.doctor.br.fragment.manage.QuanbuFragment#refreshOrderListVisible}全部页面
     * @receiver {@link com.doctor.br.fragment.main.ManageFragment#refreshOrderListVisible}管理页面
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void refreshOrderListVisible(OrderListVisibleEvent orderListVisibleEvent) {
        if (orderListVisibleEvent != null) {
            isShow = orderListVisibleEvent.isShow();
            if (mLoadingDialog == null) {
                mLoadingDialog = LoadingDialog.getInstance(getActivity());
            }
            getYiguoqiOrderListRequest("1");
        }
    }

    /**
     * 网络请求待付款订单列表
     *
     * @param page 打算请求的列表页码
     */
    private void getYiguoqiOrderListRequest(String page) {
        emptyView.setVisibility(View.GONE);
        if (!isShow) {
            orderList.clear();
            yiguoqiRecylerAdapter.notifyDataSetChanged();
            SpannableString spannableString = new SpannableString("显示用药订单，点击页面右上角的眼睛标识");
            Drawable drawable = ContextCompat.getDrawable(getActivity(), R.drawable.eye_open);
            drawable.setBounds(0, 0, DensityUtils.dip2px(getActivity(), 22), DensityUtils.dip2px(getActivity(), 22));
            ImageSpan imageSpan = new ImageSpan(drawable);
            spannableString.setSpan(imageSpan, spannableString.length() - 4, spannableString.length() - 2, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            goneTv.setText(spannableString);
            goneTv.setVisibility(View.VISIBLE);
            return;
        }
        goneTv.setVisibility(View.GONE);
        Map<String, String> map = new HashMap<>();
        map.put("payState", "7");//1 已完成 2 待支付 -1 全部 7 过期订单
        map.put("pageSize", "20");//每页条数
        map.put("page", page);//当前页码
//        map.put("userId","0000000020103");//测试使用
        getOrderListCallBack = addHttpPostRequest(HttpUrlManager.ORDER_LIST, map, YiguoqiOrderListBean.class, this);
    }


    /**
     * 重新启用订单网络请求
     *
     * @param position 点击的position位置
     */
    private void reUseOrderReuqest(int position) {
        Map<String, String> map = new HashMap<>();
        map.put("orderId", orderList.get(position).getOrderId());
        reUseOrderCallBack = addHttpPostRequest(HttpUrlManager.REUSE_ORDER, map, null, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (emptyView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.ORDER_LIST://已过期订单列表
                bgaRefreshLayout.endRefreshing();
                bgaRefreshLayout.endLoadingMore();
                if (result.isRequestSuccessed()) {
                    YiguoqiOrderListBean yiguoqiOrderListBean = (YiguoqiOrderListBean) result.getBodyObject();
                    totalPage = yiguoqiOrderListBean.getTotalPageSize();
                    currentPage = yiguoqiOrderListBean.getCurrentPage();
                    if (currentPage == 1) {
                        if (yiguoqiOrderListBean.getList().size() == 0) {
                            emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                            emptyView.setEmptyText("暂无订单记录");
                            emptyView.setEmptyImgResource(R.drawable.empty_no_order);
                            emptyView.setVisibility(View.VISIBLE);
                            return;
                        }
                        orderList.clear();
                    }
                    orderList.addAll(yiguoqiOrderListBean.getList());
                    if (currentPage == 1 && orderList.size() > 0) {
                        hasData = true;
                    } else {
                        hasData = false;
                    }
                    showHint();
                    if (currentPage >= totalPage) {
                        orderList.add(new YiguoqiOrderListBean.ListBean(true));
                    }
                    yiguoqiRecylerAdapter.notifyDataSetChanged();
                } else {
                    if (totalPage == -1) {
                        emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                        emptyView.setVisibility(View.VISIBLE);
//                        bgaRefreshLayout.setVisibility(View.GONE);
                        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
                            @Override
                            public void onReload() {
                                getYiguoqiOrderListRequest("1");
                            }
                        });
                    }
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.REUSE_ORDER://重新启用
                if (result.isRequestSuccessed()) {
                    if (reUseDialog != null && reUseDialog.isShowing()) {
                        reUseDialog.dismiss();
                    }
                    ToastUtils.showShortMsg(getActivity(), "启用成功");
                    if (reUsePosition != -1) {
                        orderList.remove(reUsePosition);
                        yiguoqiRecylerAdapter.notifyDataSetChanged();
                    }
                    refreshDaifukuanOrder();
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                reUsePosition = -1;
                break;
            default:
                break;
        }
    }

    /**
     * 刷新待付款订单界面
     *
     * @receive {@link DaifukuanFragment#refreshOrderList(RefreshOrderEvent)}
     */
    private void refreshDaifukuanOrder() {
        EventBusUtils.post(new RefreshOrderEvent(true));
    }

    @Override
    public void onDestroyView() {
        EventBusUtils.unRegister(this);
        if (getOrderListCallBack != null) {
            getOrderListCallBack.cancel();
        }
        if (reUseOrderCallBack != null) {
            reUseOrderCallBack.cancel();
        }
        super.onDestroyView();
    }


}
