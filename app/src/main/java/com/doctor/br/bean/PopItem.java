package com.doctor.br.bean;

import android.text.SpannableString;
import java.io.Serializable;

/**
 * created by Sunxiaxia on 2017/11/15 16:02
 * description: 底部弹出框的item 数据
 */
public class PopItem implements Serializable {
    private String id;
    private String name;
    private SpannableString spannableName; // 新增：支持富文本样式
    private String value;
    private int position;
    private boolean isMark = false;
    private int resId;
    private int iconId;
    private String age;//患者年龄
    private String isPregnant;//是否怀孕 0 不怀孕 1 怀孕
    private String sex;//患者性别 1男 0 女
    private String birthday;//出生日期
    private boolean custom = false;//标记为自定义的煎法
    private boolean deleteEnable = true;//可删除
    private boolean clickEnable = true;//可点击

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public boolean isCustom() {
        return custom;
    }

    public void setCustom(boolean custom) {
        this.custom = custom;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public SpannableString getSpannableName() {
        return spannableName;
    }

    public void setSpannableName(SpannableString spannableName) {
        this.spannableName = spannableName;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public boolean isMark() {
        return isMark;
    }

    public void setMark(boolean mark) {
        isMark = mark;
    }
    public void setIsMark(boolean isMark) {
        this.isMark = isMark;
    }
    public int getResId() {
        return resId;
    }

    public void setResId(int resId) {
        this.resId = resId;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getIsPregnant() {
        return isPregnant;
    }

    public void setIsPregnant(String isPregnant) {
        this.isPregnant = isPregnant;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public int getIconId() {
        return iconId;
    }

    public void setIconId(int iconId) {
        this.iconId = iconId;
    }

    public boolean isDeleteEnable() {
        return deleteEnable;
    }

    public void setDeleteEnable(boolean deleteEnable) {
        this.deleteEnable = deleteEnable;
    }

    public boolean isClickEnable() {
        return clickEnable;
    }

    public void setClickEnable(boolean clickEnable) {
        this.clickEnable = clickEnable;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
