package com.doctor.br.view;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Handler;

import androidx.annotation.DrawableRes;
import androidx.annotation.StringRes;

import android.text.SpannableString;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.doctor.br.bean.PopItem;
import com.doctor.yy.R;

import org.newapp.ones.base.adapter.BaseAdapter;
import org.newapp.ones.base.adapter.ViewHolder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * Author:sunxiaxia
 * createdDate:2017/11/14
 * description:从屏幕底部弹出框
 * 弹框布局分三部分：1、头部（包含标题及关闭按钮）2、内容部分，通过listView显示；3、底部控件（可根据需求自由显示）
 * 主要功能有：弹框的显示与隐藏、弹框标题与内容设置及内容更新、弹框内容点击回调处理、弹框底部控件显示与隐藏、底部控件text的设置及icon的设置，底部控件点击事件处理
 */

public class BottomPopWindow extends PopupWindow implements View.OnClickListener {
    private Context mContext;
    private ImageView close_img;
    private TextView title_tv;
    private ListView pop_lv;
    private List<PopItem> mList;//弹框中的数据集合
    private String mPopTitle = "";
    private OnPopItemClickListener onPopItemClickListener;//弹框中数据点击回调
    private OnPopItemLongClickListener onPopItemLongClickListener;//弹框中数据长按回调
    private OnBottomViewClickListener mOnBottomViewClickListener;//弹框底部view点击回调
    private OnQuestionIconClickListener mOnQuestionIconClickListener;//问号图标点击回调
    private LinearLayout bottom_view;
    private ImageView bottom_icon;
    private TextView bottom_text;
    private TextView pop_action_tv;
    private TextView delete_view;
    private ImageView question_icon;
    private PopAdapter mPopAdapter;
    private boolean deleteEnable = false;
    private boolean deleteSelectEnable = false;

    public BottomPopWindow(Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public BottomPopWindow(Context context, String popTitle) {
        super(context);
        this.mContext = context;
        mPopTitle = popTitle;
        initView();
    }

    public BottomPopWindow(Context context, List<PopItem> list) {
        super(context);
        this.mContext = context;
        mList = list;
        initView();
    }

    public BottomPopWindow(Context context, String title, List<PopItem> list) {
        super(context);
        this.mContext = context;
        this.mPopTitle = title;
        this.mList = list;
        initView();
    }

    /**
     * 初始化控件
     */
    private void initView() {
        View contentView = LayoutInflater.from(mContext).inflate(R.layout.bottom_popwindow_layout, null);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setBackgroundDrawable(new BitmapDrawable());
        contentView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dimissPop();
            }
        });

        setContentView(contentView);
        close_img = ((ImageView) contentView.findViewById(R.id.close_img));
        title_tv = ((TextView) contentView.findViewById(R.id.pop_title_tv));
        pop_lv = ((ListView) contentView.findViewById(R.id.pop_lv));
        bottom_view = ((LinearLayout) contentView.findViewById(R.id.bottom_view));
        bottom_icon = ((ImageView) contentView.findViewById(R.id.bottom_icon));
        bottom_text = ((TextView) contentView.findViewById(R.id.bottom_text));
        pop_action_tv = ((TextView) contentView.findViewById(R.id.pop_action_tv));
        delete_view = ((TextView) contentView.findViewById(R.id.delete_view));
        question_icon = ((ImageView) contentView.findViewById(R.id.question_icon));
        contentView.findViewById(R.id.rl_title).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //消费点击事件
            }
        });
        title_tv.setText(mPopTitle);
        close_img.setOnClickListener(this);

        // 问号图标点击事件
        if (question_icon != null) {
            question_icon.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnQuestionIconClickListener != null) {
                        mOnQuestionIconClickListener.onQuestionIconClick();
                    }
                }
            });
        }
        pop_lv.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (mPopAdapter != null && mPopAdapter.selectEnable) {
                    mPopAdapter.setItemClicked(mList.get(position), position);
                } else {
                    if (onPopItemClickListener != null) {
                        onPopItemClickListener.onPopItemClick(position, mList.get(position));
                    }
                }
            }
        });
        pop_lv.setOnItemLongClickListener(new AdapterView.OnItemLongClickListener() {

            @Override
            public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
                if (mPopAdapter != null && !mPopAdapter.selectEnable && onPopItemLongClickListener != null) {
                    onPopItemLongClickListener.onPopItemLongClick(position, mList.get(position));
                }
                return true;
            }
        });
        pop_action_tv.setOnClickListener(v -> setDeleteSelectable(!deleteSelectEnable));
        delete_view.setOnClickListener(this);

    }

    /**
     * 重置弹窗状态
     * 在显示弹窗前调用此方法，确保弹窗状态被完全重置
     */
    public void resetState() {
        // 重置删除相关状态
        setDeleteEnable(false);
        setDeleteSelectable(false);

        // 隐藏底部按钮
        setBottomViewGone();

        // 清除底部按钮图标和文本
        if (bottom_icon != null) {
            bottom_icon.setVisibility(View.GONE);
        }

        // 清除问号图标
        setQuestionIconGone();

        // 清除监听器
        onPopItemClickListener = null;
        onPopItemLongClickListener = null;
        mOnBottomViewClickListener = null;
        mOnQuestionIconClickListener = null;
    }

    /**
     * 从屏幕底部弹出
     */
    public void showPop() {
        if (((Activity) mContext).getWindow().isActive()) {
            showAtLocation(((Activity) mContext).getWindow().getDecorView(), Gravity.BOTTOM,
                    0, 0);

        } else {
            new Handler().postDelayed(new Runnable() {

                @Override
                public void run() {
                    if (((Activity) mContext).getWindow().isActive()) {
                        showAtLocation(((Activity) mContext).getWindow().getDecorView(),
                                Gravity.BOTTOM, 0, 0);
                    }
                }
            }, 600);
        }

    }

    /**
     * 隐藏popWindow
     */
    public void dimissPop() {
        try {
            setDeleteSelectable(false);
            super.dismiss();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void setDeleteEnable(boolean deleteEnable) {
        setDeleteSelectable(false);
        this.deleteEnable = deleteEnable;
        if (pop_action_tv != null) {
            pop_action_tv.setVisibility(deleteEnable ? View.VISIBLE : View.GONE);
        }
        if (delete_view != null) {
            delete_view.setVisibility(View.GONE);
        }
        if (pop_action_tv != null) {
            pop_action_tv.setText("删除");
        }
    }

    private void setDeleteSelectable(boolean deleteSelectEnable) {
        if (!deleteEnable) {
            return;
        }
        this.deleteSelectEnable = deleteSelectEnable;
        if (pop_action_tv != null) {
            if (deleteSelectEnable) {
                pop_action_tv.setText("取消");
            } else {
                pop_action_tv.setText("删除");
            }
        }
        if (delete_view != null) {
            delete_view.setVisibility(deleteSelectEnable ? View.VISIBLE : View.GONE);
        }
        if (bottom_view != null) {
            bottom_view.setVisibility(deleteSelectEnable ? View.GONE : View.VISIBLE);
        }
        if (mPopAdapter != null) {
            mPopAdapter.setSelectEnable(deleteSelectEnable);
        }
    }

    /**
     * 设置底部控件显示
     */
    public void setBottomViewVisible() {
        if (bottom_view != null) {
            bottom_view.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 设置底部控件隐藏
     */
    public void setBottomViewGone() {
        if (bottom_view != null) {
            bottom_view.setVisibility(View.GONE);
        }
    }

    /**
     * 设置底部view点击事件
     */
    public void setOnBottomViewClickListener(OnBottomViewClickListener listener) {
        mOnBottomViewClickListener = listener;
        bottom_view.setOnClickListener(this);

    }

    /**
     * 设置底部view 图标
     *
     * @param drawable
     */
    public void setBottomIcon(Drawable drawable) {
        if (bottom_icon != null && drawable != null) {
            bottom_icon.setImageDrawable(drawable);
        }
    }

    /**
     * 设置底部view 图标
     *
     * @param resId
     */
    public void setBottomIcon(@DrawableRes int resId) {
        if (bottom_icon != null) {
            bottom_icon.setImageResource(resId);
        }
    }

    /**
     * @param str 设置底部view 文字
     */
    public void setBottomText(CharSequence str) {
        if (bottom_text != null && !TextUtils.isEmpty(str)) {
            bottom_text.setText(str);
        }

    }

    /**
     * @param resId 设置底部view 文字
     */
    public void setBottomText(@StringRes int resId) {
        if (bottom_text != null) {
            bottom_text.setText(resId);
        }

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.close_img:
                dimissPop();
                break;
            case R.id.delete_view:
            case R.id.bottom_view:
                if (mOnBottomViewClickListener != null) {
                    mOnBottomViewClickListener.onBottomViewClick(deleteEnable && deleteSelectEnable, mPopAdapter == null ?
                            Collections.emptyList() : mPopAdapter.selectContent);
                }

                break;
        }
    }

    /**
     * @param listener pop Item 点击事件
     */
    public void setOnItemClickListener(OnPopItemClickListener listener) {
        onPopItemClickListener = listener;
    }

    /**
     * @param listener pop Item 长按事件
     */
    public void setOnItemLongClickListener(OnPopItemLongClickListener listener) {
        onPopItemLongClickListener = listener;
    }

    /**
     * @param titleStr 标题
     *                 设置窗口标题
     */
    public void setPopTitle(String titleStr) {
        if (title_tv != null) {
            title_tv.setText(titleStr);
        }
    }

    /**
     * @param titleStr SpannableString 标题
     *                 设置窗口标题
     */
    public void setPopSnannableTitle(SpannableString titleStr) {
        if (title_tv != null) {
            title_tv.setText(titleStr);
        }
    }


    /**
     * @param resId 标题
     *              设置窗口标题
     */
    public void setPopTitle(@StringRes int resId) {
        if (title_tv != null) {
            title_tv.setText(resId);
        }
    }

    /**
     * @param data 弹框内容
     */
    public void setPopContentData(List<PopItem> data) {
        this.mList = data;
        mPopAdapter = new PopAdapter(mContext, mList, R.layout.popwindow_item_layout);
        pop_lv.setAdapter(mPopAdapter);
        pop_lv.setSelection(0);
    }


    /**
     * @param list 更新popWindow 列表内容
     */
    public void upDatePopContent(List<PopItem> list) {
        this.mList = list;
        if (mPopAdapter != null) {

            mPopAdapter.notifyDataSetChanged();
        }
    }

    public class PopAdapter extends BaseAdapter {
        private List<PopItem> popContent;
        private List<PopItem> selectContent = new ArrayList<>();
        private boolean selectEnable;

        public PopAdapter(Context context, List<PopItem> list, int layoutResId) {
            super(context, list, layoutResId);
            this.popContent = list;
        }

        public void setSelectEnable(boolean selectEnable) {
            if (this.selectEnable != selectEnable) {
                this.selectEnable = selectEnable;
                clearSelectedContent();
            }
        }

        public void clearSelectedContent() {
            selectContent.clear();
            notifyDataSetChanged();
        }

        public void setItemClicked(PopItem popItem, int position) {
            if (!selectEnable || !popItem.isDeleteEnable()) {
                return;
            }
            if (selectContent.contains(popItem)) {
                selectContent.remove(popItem);
            } else {
                selectContent.add(popItem);
            }
            notifyDataSetChanged();
        }


        @Override
        public void setViewData(ViewHolder holder, int position) {
            ImageView selectCb = ((ImageView) holder.findViewById(R.id.select_cb));
            TextView content_tv = ((TextView) holder.findViewById(R.id.item_content_tv));
            ImageView icon = ((ImageView) holder.findViewById(R.id.item_icon));
            ImageView leftIcon = ((ImageView) holder.findViewById(R.id.item_left_icon));
            // 优先使用SpannableString，如果没有则使用普通字符串
            if (popContent.get(position).getSpannableName() != null) {
                SpannableString spannableText = popContent.get(position).getSpannableName();
                if (spannableText.length() > 15) {
                    SpannableString truncatedText = new SpannableString(spannableText.subSequence(0, 15) + "…");
                    // 复制原有的样式到截断的文本
                    Object[] spans = spannableText.getSpans(0, Math.min(15, spannableText.length()), Object.class);
                    for (Object span : spans) {
                        int start = spannableText.getSpanStart(span);
                        int end = Math.min(spannableText.getSpanEnd(span), 15);
                        int flags = spannableText.getSpanFlags(span);
                        if (start < 15) {
                            truncatedText.setSpan(span, start, end, flags);
                        }
                    }
                    content_tv.setText(truncatedText);
                } else {
                    content_tv.setText(spannableText);
                }
            } else {
                content_tv.setText(popContent.get(position).getName().length() > 15 ? popContent.get(position).getName().substring(0, 15) + "…" : popContent.get(position).getName());
            }
            PopItem popItem = popContent.get(position);
            if (popContent.get(position).isMark()) {
                icon.setVisibility(View.VISIBLE);
                icon.setImageResource(popContent.get(position).getResId());
            } else {
                icon.setVisibility(View.GONE);
            }
            if (popContent.get(position).isClickEnable()) {
                content_tv.setTextColor(content_tv.getResources().getColor(R.color.br_color_theme_text));
            } else {
                content_tv.setTextColor(content_tv.getResources().getColor(R.color.br_color_black_999));
            }
            if (popContent.get(position).getIconId() != 0) {
                leftIcon.setVisibility(View.VISIBLE);
                leftIcon.setImageResource(popContent.get(position).getIconId());
            } else {
                leftIcon.setVisibility(View.GONE);
            }
            if (selectEnable && popItem.isDeleteEnable()) {
                selectCb.setVisibility(View.VISIBLE);
            } else {
                selectCb.setVisibility(View.GONE);
            }
            selectCb.setSelected(selectContent.contains(popItem));
        }

    }

    public interface OnPopItemClickListener {
        public void onPopItemClick(int position, PopItem popItem);
    }

    public interface OnPopItemLongClickListener {
        public void onPopItemLongClick(int position, PopItem popItem);
    }

    /**
     * 设置问号图标可见并设置点击事件监听器
     */
    public void setQuestionIconVisible() {
        if (question_icon != null) {
            question_icon.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 设置问号图标不可见
     */
    public void setQuestionIconGone() {
        if (question_icon != null) {
            question_icon.setVisibility(View.GONE);
        }
    }

    /**
     * 设置问号图标点击事件
     * @param listener 监听器
     */
    public void setOnQuestionIconClickListener(OnQuestionIconClickListener listener) {
        mOnQuestionIconClickListener = listener;
    }

    public interface OnBottomViewClickListener {
        public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems);
    }

    /**
     * 问号图标点击回调接口
     */
    public interface OnQuestionIconClickListener {
        public void onQuestionIconClick();
    }

}
