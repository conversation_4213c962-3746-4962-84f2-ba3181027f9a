package com.doctor.br.view;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.os.Build;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.doctor.yy.R;

import java.util.ArrayList;
import java.util.List;

/**
 * 辅料选择弹窗
 * 支持多选辅料和"不添加辅料"选项
 */
public class AuxiliaryMaterialPopWindow extends PopupWindow implements View.OnClickListener {
    private Context mContext;
    private ImageView closeImg;
    private TextView titleTv;
    private LinearLayout materialContainer;
    private CheckBox noMaterialCb;
    private TextView noMaterialBtn;
    private TextView confirmBtn;
    private View backgroundView;
    private LinearLayout contentLayout;
    
    private List<String> materialList; // 辅料列表
    private List<String> selectedMaterials = new ArrayList<>(); // 已选择的辅料
    private boolean isNoMaterial = false; // 是否选择"不添加辅料"
    private boolean hasNoMaterialOption = false; // 是否包含"不添加辅料"选项
    private OnConfirmListener onConfirmListener;
    
    public interface OnConfirmListener {
        void onConfirm(List<String> selectedMaterials, boolean isNoMaterial);
    }
    
    public AuxiliaryMaterialPopWindow(Context context) {
        super(context);
        mContext = context;
        initView();
    }
    
    public AuxiliaryMaterialPopWindow(Context context, List<String> materialList) {
        super(context);
        mContext = context;
        this.materialList = materialList;
        initView();
    }
    
    private void initView() {
        View contentView = LayoutInflater.from(mContext).inflate(R.layout.auxiliary_material_popwindow_layout, null);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setContentView(contentView);
        this.setBackgroundDrawable(new BitmapDrawable());
        this.setFocusable(true);
        this.setTouchable(true);
        this.setOutsideTouchable(false);
        // 移除整体弹窗动画，改为自定义动画
        
        findViews(contentView);
        setListeners();
        initData();
    }
    
    private void findViews(View contentView) {
        backgroundView = contentView.findViewById(R.id.container);
        contentLayout = contentView.findViewById(R.id.content_layout);
        closeImg = contentView.findViewById(R.id.close_img);
        titleTv = contentView.findViewById(R.id.title_tv);
        materialContainer = contentView.findViewById(R.id.material_container);
        noMaterialCb = contentView.findViewById(R.id.no_material_cb);
        noMaterialBtn = contentView.findViewById(R.id.no_material_btn);
        confirmBtn = contentView.findViewById(R.id.confirm_btn);
        
        titleTv.setText("请选择辅料");
        
        // 设置背景点击事件，点击空白区域关闭弹窗
        backgroundView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissWithAnimation();
            }
        });
        
        // 设置内容区域点击事件，消费点击事件，防止点击内容区域时关闭弹窗
        contentView.findViewById(R.id.rl_title).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 消费点击事件
            }
        });
        
        // 为确认按钮的父容器也设置点击事件
        View bottomView = contentView.findViewById(R.id.bottom_view);
        if (bottomView != null) {
            bottomView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    android.util.Log.d("AuxiliaryMaterial", "bottom_view点击，触发确认事件");
                    // 调用确认逻辑
                    if (onConfirmListener != null) {
                        android.util.Log.d("AuxiliaryMaterial", "bottom_view点击回调监听器存在，执行回调");
                        onConfirmListener.onConfirm(selectedMaterials, isNoMaterial);
                    } else {
                        android.util.Log.d("AuxiliaryMaterial", "bottom_view点击回调监听器为空！");
                    }
                    dismissWithAnimation();
                }
            });
        }
    }
    
    private void setListeners() {
        android.util.Log.d("AuxiliaryMaterial", "设置点击监听器");
        closeImg.setOnClickListener(this);
        confirmBtn.setOnClickListener(this);
        android.util.Log.d("AuxiliaryMaterial", "确认按钮监听器已设置，confirmBtn: " + confirmBtn);
        
        // 设置"不添加辅料"按钮点击监听器
        if (noMaterialBtn != null) {
            noMaterialBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    boolean newSelectedState = !noMaterialBtn.isSelected();
                    noMaterialBtn.setSelected(newSelectedState);
                    
                    android.util.Log.d("AuxiliaryMaterial", "不添加辅料选择状态改变: " + newSelectedState);
                    isNoMaterial = newSelectedState;
                    
                    if (newSelectedState) {
                        // 选择"不添加辅料"时，清空所有辅料选择
                        selectedMaterials.clear();
                        updateMaterialCheckBoxes();
                        android.util.Log.d("AuxiliaryMaterial", "已清空所有辅料选择");
                    }
                    
                    // 同步CheckBox状态（保持兼容性）
                    if (noMaterialCb != null) {
                        noMaterialCb.setChecked(newSelectedState);
                    }
                }
            });
        }
        
        // 保留原有的CheckBox监听器（兼容性）
        noMaterialCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                android.util.Log.d("AuxiliaryMaterial", "不添加辅料CheckBox选择状态改变: " + isChecked);
                isNoMaterial = isChecked;
                if (isChecked) {
                    // 选择"不添加辅料"时，清空所有辅料选择
                    selectedMaterials.clear();
                    updateMaterialCheckBoxes();
                    android.util.Log.d("AuxiliaryMaterial", "已清空所有辅料选择");
                }
                
                // 同步按钮状态
                if (noMaterialBtn != null) {
                    noMaterialBtn.setSelected(isChecked);
                }
            }
        });
    }
    
    private void initData() {
        if (materialList != null && !materialList.isEmpty()) {
            // 检查是否包含"不添加辅料"选项
            hasNoMaterialOption = materialList.contains("不添加辅料");
            // 控制"不添加辅料"控件的显示
            if (noMaterialCb != null) {
                noMaterialCb.setVisibility(View.GONE); // 隐藏CheckBox
            }
            if (noMaterialBtn != null) {
                noMaterialBtn.setVisibility(hasNoMaterialOption ? View.VISIBLE : View.GONE);
            }
            createMaterialCheckBoxes();
        }
    }
    
    private void createMaterialCheckBoxes() {
        materialContainer.removeAllViews();
        
        // 获取屏幕宽度和按钮的大致宽度来计算每行可以放多少个按钮
        int screenWidth = mContext.getResources().getDisplayMetrics().widthPixels;
        int containerPadding = (int) (30 * mContext.getResources().getDisplayMetrics().density); // 左右各15dp的容器内边距
        int availableWidth = screenWidth - containerPadding;
        
        LinearLayout currentRow = null;
        int currentRowWidth = 0;
        
        for (String material : materialList) {
            // 过滤掉"不添加辅料"选项，避免重复显示
            if ("不添加辅料".equals(material)) {
                continue;
            }
            
            // 创建临时按钮来测量宽度
            TextView tempButton = new TextView(mContext);
            tempButton.setText(material);
            tempButton.setTextSize(15);
            int paddingHorizontal = (int) (15 * mContext.getResources().getDisplayMetrics().density); // 15dp转像素
            int paddingVertical = (int) (10 * mContext.getResources().getDisplayMetrics().density); // 10dp转像素
            tempButton.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
            tempButton.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
            
            int buttonWidth = tempButton.getMeasuredWidth();
            int buttonHorizontalMargin = (int) (30 * mContext.getResources().getDisplayMetrics().density); // 左右各15dp margin
            int totalButtonWidth = buttonWidth + buttonHorizontalMargin;
            
            // 如果当前行为空或者当前行放不下这个按钮，创建新行
            if (currentRow == null || (currentRowWidth + totalButtonWidth) > availableWidth) {
                currentRow = new LinearLayout(mContext);
                currentRow.setOrientation(LinearLayout.HORIZONTAL);
                currentRow.setGravity(android.view.Gravity.START); // 左对齐
                LinearLayout.LayoutParams rowParams = new LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT);
                currentRow.setLayoutParams(rowParams);
                materialContainer.addView(currentRow);
                currentRowWidth = 0;
            }
            
            // 使用TextView作为按钮
            TextView materialButton = new TextView(mContext);
            materialButton.setText(material);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                materialButton.setTextColor(mContext.getResources().getColorStateList(R.color.auxiliary_material_text_color, null));
                materialButton.setBackground(mContext.getResources().getDrawable(R.drawable.auxiliary_material_button_selector, null));
            } else {
                materialButton.setTextColor(mContext.getResources().getColorStateList(R.color.auxiliary_material_text_color));
                materialButton.setBackground(mContext.getResources().getDrawable(R.drawable.auxiliary_material_button_selector));
            }
            materialButton.setTextSize(15);
            materialButton.setGravity(android.view.Gravity.CENTER);
            // 设置内边距：左右15dp，上下10dp
            int buttonPaddingHorizontal = (int) (15 * mContext.getResources().getDisplayMetrics().density);
            int buttonPaddingVertical = (int) (10 * mContext.getResources().getDisplayMetrics().density);
            materialButton.setPadding(buttonPaddingHorizontal, buttonPaddingVertical, buttonPaddingHorizontal, buttonPaddingVertical);
            materialButton.setClickable(true);
            materialButton.setFocusable(true);
            
            // 设置按钮为自适应宽度，高度为40dp
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    (int) (40 * mContext.getResources().getDisplayMetrics().density)); // 40dp高度
            
            // 设置间距：左右15dp，上下7.5dp
            int horizontalMargin = (int) (15 * mContext.getResources().getDisplayMetrics().density);
            int verticalMargin = (int) (7.5 * mContext.getResources().getDisplayMetrics().density);
            params.setMargins(horizontalMargin, verticalMargin, horizontalMargin, verticalMargin);
            materialButton.setLayoutParams(params);
            
            // 设置初始状态
            boolean isSelected = selectedMaterials.contains(material);
            materialButton.setSelected(isSelected);
            
            materialButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    TextView button = (TextView) v;
                    String materialName = button.getText().toString();
                    boolean isCurrentlySelected = button.isSelected();
                    boolean newSelectedState = !isCurrentlySelected;
                    
                    button.setSelected(newSelectedState);
                    
                    android.util.Log.d("AuxiliaryMaterial", "辅料选择状态改变: " + materialName + " = " + newSelectedState);
                    
                    if (newSelectedState) {
                        if (!selectedMaterials.contains(materialName)) {
                            selectedMaterials.add(materialName);
                            android.util.Log.d("AuxiliaryMaterial", "添加辅料: " + materialName + ", 当前选择: " + selectedMaterials.toString());
                        }
                        // 选择辅料时，取消"不添加辅料"选择（仅当有该选项时）
                        if (isNoMaterial && hasNoMaterialOption) {
                            isNoMaterial = false;
                            if (noMaterialCb != null) {
                                noMaterialCb.setChecked(false);
                            }
                            if (noMaterialBtn != null) {
                                noMaterialBtn.setSelected(false);
                            }
                            android.util.Log.d("AuxiliaryMaterial", "取消'不添加辅料'选择");
                        }
                    } else {
                        selectedMaterials.remove(materialName);
                        android.util.Log.d("AuxiliaryMaterial", "移除辅料: " + materialName + ", 当前选择: " + selectedMaterials.toString());
                    }
                }
            });
            
            currentRow.addView(materialButton);
            currentRowWidth += totalButtonWidth;
        }
    }
    
    private void updateMaterialCheckBoxes() {
        for (int i = 0; i < materialContainer.getChildCount(); i++) {
            View child = materialContainer.getChildAt(i);
            if (child instanceof LinearLayout) {
                LinearLayout row = (LinearLayout) child;
                for (int j = 0; j < row.getChildCount(); j++) {
                    View rowChild = row.getChildAt(j);
                    if (rowChild instanceof TextView) {
                        TextView materialButton = (TextView) rowChild;
                        String materialName = materialButton.getText().toString();
                        boolean isSelected = selectedMaterials.contains(materialName);
                        materialButton.setSelected(isSelected);
                    }
                }
            }
        }
    }
    
    @Override
    public void onClick(View v) {
        android.util.Log.d("AuxiliaryMaterial", "弹窗点击事件触发，View ID: " + v.getId() + ", close_img ID: " + R.id.close_img + ", confirm_btn ID: " + R.id.confirm_btn);
        
        if (v.getId() == R.id.close_img) {
            android.util.Log.d("AuxiliaryMaterial", "关闭按钮点击");
            dismissWithAnimation();
        } else if (v.getId() == R.id.confirm_btn) {
            android.util.Log.d("AuxiliaryMaterial", "确认按钮点击，选择的辅料: " + selectedMaterials.toString() + ", 不添加辅料: " + isNoMaterial);
            if (onConfirmListener != null) {
                android.util.Log.d("AuxiliaryMaterial", "回调监听器存在，执行回调");
                onConfirmListener.onConfirm(selectedMaterials, isNoMaterial);
            } else {
                android.util.Log.d("AuxiliaryMaterial", "回调监听器为空！");
            }
            dismissWithAnimation();
        } else {
            android.util.Log.d("AuxiliaryMaterial", "未知的点击事件，View ID: " + v.getId());
        }
    }
    
    public void setOnConfirmListener(OnConfirmListener listener) {
        this.onConfirmListener = listener;
    }
    
    public void setMaterialList(List<String> materialList) {
        this.materialList = materialList;
        if (materialList != null && !materialList.isEmpty()) {
            // 检查是否包含"不添加辅料"选项
            hasNoMaterialOption = materialList.contains("不添加辅料");
            // 控制"不添加辅料"控件的显示
            if (noMaterialCb != null) {
                noMaterialCb.setVisibility(View.GONE); // 隐藏CheckBox
            }
            if (noMaterialBtn != null) {
                noMaterialBtn.setVisibility(hasNoMaterialOption ? View.VISIBLE : View.GONE);
            }
            createMaterialCheckBoxes();
        }
    }
    
    public void setSelectedMaterials(List<String> selectedMaterials, boolean isNoMaterial) {
        this.selectedMaterials = new ArrayList<>(selectedMaterials);
        this.isNoMaterial = isNoMaterial;
        if (noMaterialCb != null && hasNoMaterialOption) {
            noMaterialCb.setChecked(isNoMaterial);
        }
        if (noMaterialBtn != null && hasNoMaterialOption) {
            noMaterialBtn.setSelected(isNoMaterial);
        }
        updateMaterialCheckBoxes();
    }
    
    public void show(View parent) {
        if (!((Activity) mContext).isFinishing()) {
            android.util.Log.d("AuxiliaryMaterial", "显示辅料弹窗");
            this.showAtLocation(parent, Gravity.BOTTOM, 0, 0);
            showWithAnimation();
            
            // 检查确认按钮的状态
            android.util.Log.d("AuxiliaryMaterial", "确认按钮状态 - 可点击: " + confirmBtn.isClickable() + ", 可见: " + (confirmBtn.getVisibility() == View.VISIBLE) + ", 启用: " + confirmBtn.isEnabled());
        }
    }
    
    /**
     * 显示弹窗时的动画效果
     */
    private void showWithAnimation() {
        if (backgroundView != null && contentLayout != null) {
            // 背景淡入动画
            Animation fadeIn = AnimationUtils.loadAnimation(mContext, R.anim.fade_in);
            backgroundView.startAnimation(fadeIn);
            
            // 内容从底部滑入动画
            Animation slideIn = AnimationUtils.loadAnimation(mContext, R.anim.slide_in_bottom);
            contentLayout.startAnimation(slideIn);
        }
    }
    
    /**
     * 隐藏弹窗时的动画效果
     */
    private void dismissWithAnimation() {
        if (backgroundView != null && contentLayout != null) {
            // 背景淡出动画
            Animation fadeOut = AnimationUtils.loadAnimation(mContext, R.anim.fade_out);
            // 内容向底部滑出动画
            Animation slideOut = AnimationUtils.loadAnimation(mContext, R.anim.slide_out_bottom);
            
            // 设置动画结束监听，在动画结束后真正关闭弹窗
            fadeOut.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {}
                
                @Override
                public void onAnimationEnd(Animation animation) {
                    dismiss();
                }
                
                @Override
                public void onAnimationRepeat(Animation animation) {}
            });
            
            backgroundView.startAnimation(fadeOut);
            contentLayout.startAnimation(slideOut);
        } else {
            dismiss();
        }
    }
}