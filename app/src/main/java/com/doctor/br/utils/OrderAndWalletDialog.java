package com.doctor.br.utils;

import android.os.CountDownTimer;
import androidx.core.content.ContextCompat;
import androidx.appcompat.app.AlertDialog;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import com.doctor.br.app.AppContext;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.BaseActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.OnRequestListener;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 类描述：订单和钱包页面控制显示的dialog
 * 创建人：ShiShaoPo
 * 创建时间：2017/11/28
 */

public class OrderAndWalletDialog implements OnRequestListener {
    private BaseActivity baseActivity;
    private AlertDialog showDialog;//切换为显示状态的对话框
    private ConfirmDialog dismissDialog;//切换为隐藏状态的对话框
    private WhichActivity whichActivity;//在哪个界面显示
    private VisibilityListener visibilityListener;//更改成功之后的回调，用于更新UI
    private boolean isShow;//当前状态,true当前为显示状态打算切换为隐藏状态，false当前为隐藏状态打算切换成显示状态
    //界面下的控件
    @BindView(R.id.title_tv)
    TextView titleTv;
    @BindView(R.id.phone_tv)
    TextView phoneTv;
    @BindView(R.id.send_code_btn)
    TextView sendCodeBtn;
    @BindView(R.id.code_et)
    EditText codeEt;
    @BindView(R.id.cancel_btn)
    TextView cancelBtn;
    @BindView(R.id.confirm_btn)
    TextView confirmBtn;
    //当前用户手机号码
    private String phoneNumber;

    private RequestCallBack getCodeCallBack;//网络请求获取验证码回调
    private RequestCallBack visibilityCallBack;//网络请求更改显示状态回调

    private CountDownTimer countDownTimer;//倒计时
    private boolean isJustCheckCode = false;

    /**
     * @param baseActivity       要显示的界面，最好用BaseActivity,因为要用到网络请求
     * @param isShow             当前状态，true为显示状态，false为隐藏状态
     * @param whichActivity      在哪个界面显示
     * @param visibilityListener 更改成功之后的回调，用于更新UI
     */
    public OrderAndWalletDialog(BaseActivity baseActivity, boolean isShow, WhichActivity whichActivity, VisibilityListener visibilityListener) {
        this.baseActivity = baseActivity;
        this.isShow = isShow;
        this.whichActivity = whichActivity;
        this.visibilityListener = visibilityListener;
        init();
    }

    /**
     * 显示对话框
     */
    public void show() {
        if (!isShow) {
            if (showDialog != null && !showDialog.isShowing()) {
                //这里会将倒计时重置
//                if (countDownTimer != null) {
//                    countDownTimer.cancel();
//                    switchCodeState(true);
//                    sendCodeBtn.setText(R.string.get_validation_code);
//                    codeEt.setText("");
//                }
                showDialog.show();
            }
        } else {
            if (dismissDialog != null && !dismissDialog.isShowing()) {
                dismissDialog.show();
            }
        }
    }

    /**
     * 取消正在进行的网络回调和倒计时
     */
    public void cancel() {
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        if (getCodeCallBack != null) {
            getCodeCallBack.cancel();
        }
        if (visibilityCallBack != null) {
            visibilityCallBack.cancel();
        }
        baseActivity = null;
    }

    public interface VisibilityListener {
        /**
         * 网络请求成功之后的回调
         *
         * @param isShow 更改后的状态，用于更新UI
         */
        void changeSuccess(boolean isShow);
    }

    /**
     * 初始化两种状态的对话框
     */
    private void init() {
        phoneNumber = SharedPreferenceUtils.getString(baseActivity, PublicParams.USER_TEL);
        String showTitle = "";
        String disContent = "";
        switch (whichActivity) {
            case ORDER_ACTIVITY:
                showTitle = "显示用药订单";
                disContent = "是否隐藏您的用药订单？";
                break;
            case WALLET_ACTIVITY:
                showTitle = "显示钱包";
                disContent = "是否隐藏我的钱包？";
                break;
            case PICTURE_ORDER_ACTIVITY:
                showTitle = "显示拍照药方";
                disContent = "是否隐藏拍照药方？";
                break;
            default:
                break;
        }
        View view2 = LayoutInflater.from(baseActivity).inflate(R.layout.dialog_order_and_wallet, (ViewGroup) baseActivity.getWindow().getDecorView(), false);//这种方法不会使布局自适应
        ButterKnife.bind(this, view2);
        titleTv.setText(showTitle);
        phoneTv.setText(phoneNumber);
        showDialog = new AlertDialog.Builder(baseActivity, R.style.alert_dialog_style)
                .setView(view2)
//                .setCancelable(false)
                .create();

        dismissDialog = ConfirmDialog.getInstance(baseActivity)
                .setDialogContent(disContent)
                .setPositiveText("隐藏")
                .setNavigationText("取消")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        if (dismissDialog != null && dismissDialog.isShowing()) {
                            dismissDialog.dismiss();
                        }
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        setVisibility();
                    }
                });

        countDownTimer = new CountDownTimer(60 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                switchCodeState(false);
                sendCodeBtn.setText("重新发送(" + millisUntilFinished / 1000 + "s" + ")");
            }

            @Override
            public void onFinish() {
                switchCodeState(true);
                sendCodeBtn.setText(R.string.get_validation_code);
            }
        };
    }


    @OnClick({R.id.send_code_btn, R.id.cancel_btn, R.id.confirm_btn})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.send_code_btn:
                if (sendCodeBtn.getText().toString().contains("重新发送")) {
                    return;
                }
                getCodeRequest(phoneNumber, "1", null);
                if (codeEt != null) {
                    codeEt.setText("");
                }
                break;
            case R.id.cancel_btn:
                if (showDialog != null && showDialog.isShowing()) {
                    showDialog.dismiss();
                }
                break;
            case R.id.confirm_btn:
                if (isJustCheckCode) { //校验验证码，提现使用
                    requestCheckSmsCode();
                } else {
                    if (TextUtils.isEmpty(codeEt.getText())) {
                        ToastUtils.showShortMsg(baseActivity, "请输入验证码");
                        return;
                    }
                    setVisibility();
                }

                break;
            default:
                break;
        }
    }

    private void requestCheckSmsCode() {
        String code = codeEt.getText().toString().trim();
        String mobile = phoneTv.getText().toString().trim();
        if (TextUtils.isEmpty(mobile)) {
            return;
        }
        if (TextUtils.isEmpty(code)) {
            ToastUtils.showShortMsg(AppContext.getContext(), "请输入验证码");
            return;
        }
        
        // 新版本：不再调用验证接口，直接进入提现流程
        if (visibilityListener != null) {
            visibilityListener.changeSuccess(isShow);
        }
        
        // 隐藏对话框
        if (showDialog != null && showDialog.isShowing()) {
            showDialog.dismiss();
        }
    }

    /**
     * 切换获取验证码按钮的状态
     *
     * @param clickable 是否可以点击
     */
    private void switchCodeState(boolean clickable) {
        if (clickable) {
            sendCodeBtn.setBackgroundResource(R.drawable.btn_circle_theme);
            sendCodeBtn.setTextColor(ContextCompat.getColor(baseActivity, R.color.br_color_white));
        } else {
            sendCodeBtn.setBackgroundResource(R.drawable.btn_circle_gray_line);
            sendCodeBtn.setTextColor(ContextCompat.getColor(baseActivity, R.color.br_color_black_999));
        }
    }

    /**
     * 网络请求获取验证码
     *
     * @param tel     获取验证码的手机
     * @param type    不知道为啥，反正就是1
     * @param optType 1=包括登陆、忘记密码、安全设置(修改手机号)2=包括注册
     */
    private void getCodeRequest(String tel, String type, String optType) {
        Map<String, String> map = new HashMap<>();
        map.put("tel", tel);
        map.put("type", type);
        map.put("optType", optType);
        getCodeCallBack = baseActivity.addHttpPostRequest(HttpUrlManager.GET_CODE, map, null, this);
    }

    /**
     * 网络请求更改显示状态
     * 必填 type 1 订单列表 2 我的钱包 3 拍照药方列表
     * 想要切换的状态，必填 value 1 显示 2 不显示
     * 当value=1时必填 smsCode 短信验证码
     */
    private void setVisibility() {
        Map<String, String> map = new HashMap<>();
        switch (whichActivity) {
            case ORDER_ACTIVITY:
                map.put("type", "1");
                break;
            case WALLET_ACTIVITY:
                map.put("type", "2");
                break;
            case PICTURE_ORDER_ACTIVITY:
                map.put("type", "3");
                break;
            default:
                break;
        }
        map.put("value", isShow ? "2" : "1");
        if (!isShow) {
            map.put("smsCode", codeEt.getText().toString());
        }
        visibilityCallBack = baseActivity.addHttpPostRequest(HttpUrlManager.VISIBILITY_ORDER_WALLET, map, null, this);
    }

    @Override
    public void onRequestFinished(String taskId, boolean successed, String responseStr) {

    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        baseActivity.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.GET_CODE:
                if (result.isRequestSuccessed()) {
                    countDownTimer.start();
                    ToastUtils.showShortMsg(baseActivity, "验证码已发送");
                } else {
                    RequestErrorToast.showError(baseActivity, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.VISIBILITY_ORDER_WALLET:
                if (result.isRequestSuccessed()) {
                    if (showDialog != null && showDialog.isShowing()) {
                        showDialog.dismiss();
                    }
                    if (dismissDialog != null && dismissDialog.isShowing()) {
                        dismissDialog.dismiss();
                    }
                    if (visibilityListener != null) {
                        isShow = !isShow;
                        visibilityListener.changeSuccess(isShow);
                    }
                } else {
                    RequestErrorToast.showError(baseActivity, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            // 移除 CHECK_CODE 的处理，因为新版本不再使用单独的验证接口
            default:
                break;
        }
    }

    /**
     * 用于验证码对话框，点击“确定”后校验验证码（提现使用)
     */
    public void isJustCheckCode(boolean isJustCheckCode) {
        this.isJustCheckCode = isJustCheckCode;

    }

    public void setTvTitle(String content) {
        if (titleTv != null) {
            titleTv.setText(content);
        }

    }

    public void setBtnCancel(String content) {
        if (cancelBtn != null) {
            cancelBtn.setText(content);
        }
    }

    public void setBtnConfirm(String content) {
        if (confirmBtn != null) {
            confirmBtn.setText(content);
        }

    }

    public void dismissShowDialog() {
        if (showDialog != null && showDialog.isShowing()) {
            showDialog.dismiss();
        }
    }

    /**
     * 获取用户输入的短信验证码
     * @return 短信验证码
     */
    public String getSmsCode() {
        if (codeEt != null) {
            return codeEt.getText().toString().trim();
        }
        return "";
    }


    public enum WhichActivity {
        //订单页面
        ORDER_ACTIVITY,
        //钱包页面
        WALLET_ACTIVITY,
        //拍照订单页面
        PICTURE_ORDER_ACTIVITY;
    }
}
