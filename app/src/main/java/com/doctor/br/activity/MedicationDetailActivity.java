package com.doctor.br.activity;

import android.app.Activity;
import android.content.DialogInterface;
import android.graphics.Color;
import android.os.Bundle;
import android.text.InputType;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.doctor.br.activity.medical.MedicationDrugDetailsActivity;
import com.doctor.br.bean.MedicationDetailBean;
import com.doctor.br.bean.medical.MedicineDetailMsgBean;
import com.doctor.br.bean.medical.OrderMsgBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.ClipMedicationHelper;
import com.doctor.br.utils.DecimalUtils;
import com.doctor.br.view.FlowLayout;
import com.doctor.yy.R;
import com.google.common.collect.Lists;

import org.newapp.ones.base.activity.BaseViewActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.listener.OnButtonClickListener;
import org.newapp.ones.base.network.OkHttpUtils;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.network.RequestParams;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.ActionBarView;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.EmptyView;
import org.newapp.ones.base.widgets.InputDialog;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @version 4.0.3
 * @project BrZhongYiAndroid
 * @description 用药详情界面
 * @createTime
 */
public class MedicationDetailActivity extends BaseViewActivity {
    //上个页面传递过来的数据
    //如果上个页面需要刷新数据时，需使用startForResult，返回RESULT_OK需要刷新数据
    public static final String EXTRA_ORDER_ID = "order_id";//订单id

    @BindView(R.id.tv_order_number_label)
    TextView tvOrderNumberLabel;
    @BindView(R.id.tv_order_number)
    TextView tvOrderNumber;
    @BindView(R.id.btn_copy_order_number)
    ImageView btnCopyOrderNumber;
    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.tv_age)
    TextView tvAge;
    @BindView(R.id.tv_sex)
    TextView tvSex;
    @BindView(R.id.tv_pregant)
    TextView tvPregant;
    @BindView(R.id.tv_disease)
    TextView tvDisease;
    @BindView(R.id.tv_medicine_type)
    TextView tvMedicineType;
    @BindView(R.id.rl_dosage)
    RelativeLayout rlDosage;
    @BindView(R.id.tv_avoid)
    TextView tvAvoid;
    @BindView(R.id.tv_guid)
    TextView tvGuid;
    @BindView(R.id.tv_tip_guid)
    TextView tvTipGuid;
    @BindView(R.id.tv_money)
    TextView tvMoney;
    @BindView(R.id.tv_dose_count)
    TextView tvDoseCount;
    @BindView(R.id.tv_dose_day)
    TextView tvDoseDay;
    @BindView(R.id.tv_dose_times)
    TextView tvDoseTimes;
    @BindView(R.id.tv_drink_time)
    TextView tvDrinkTime;
    @BindView(R.id.flowLayout)
    FlowLayout flowLayout;
    @BindView(R.id.ll_money)
    LinearLayout llMoney;
    @BindView(R.id.tv_tip_money)
    TextView tvTipMoney;
    @BindView(R.id.ll_medicine_type)
    LinearLayout llMedicineType;
    @BindView(R.id.ll_specification)
    LinearLayout llSpecification;
    @BindView(R.id.tv_specification)
    TextView tvSpecification;
    @BindView(R.id.emptyView)
    EmptyView emptyView;
    @BindView(R.id.ll_secret)
    LinearLayout llSecret;
    @BindView(R.id.tv_secret)
    TextView tvSecret;
    @BindView(R.id.ll_delete)
    LinearLayout llDelete;
    @BindView(R.id.ll_modify)
    LinearLayout llModify;
    @BindView(R.id.ll_clip)
    LinearLayout llClip;
    @BindView(R.id.iv_modify)
    ImageView ivModify;
    @BindView(R.id.tv_modify)
    TextView tvModify;
    @BindView(R.id.dc_id_tv)
    TextView dcIdTv;
    @BindView(R.id.picture_linear)
    LinearLayout pictureLinear;
    @BindView(R.id.picture_img)
    ImageView pictureImg;//拍照药方的图片
    @BindView(R.id.send_btn)
    Button sendBtn;//将拍照药方发送给患者的按钮
    @BindView(R.id.ll_pharmacy_code)
    LinearLayout llPharmacyCode;//药房代码
    @BindView(R.id.ll_daijian)
    LinearLayout llDaijian;
    @BindView(R.id.tv_daijian)
    TextView tvDaiJian;
    @BindView(R.id.ll_daijian_preference)
    LinearLayout llDaiJianPreference;
    @BindView(R.id.tv_daijian_preference)
    TextView tvDaiJianPreference;
    @BindView(R.id.tv_secret_type_input)
    TextView tvSecretTypeInput;
    @BindView(R.id.ll_use_method)
    LinearLayout lluseDrugMethod;
    @BindView(R.id.tv_use_method)
    TextView tvUseMethod;

    private boolean islock_delete;//用于判断是作废加的锁，还是修改剂数加的锁
    private boolean isLocking;    //是否是加锁状态，用于在dialog取消时，解锁

    public static final String ORDER_NO_PAY = "NO_PAY";//待付款   payStatus的状态
    public static final String ORDER_PAYED = "PAYED";//已付款      payStatus的状态
    public static final String ORDER_REFUND = "REFUND";//退款      payStatus的状态

    private static final String ORDER_STATUS_FOR_PAY = "WAIT_FOR_PAY";// 未付款                orderStatus的状态
    private static final String ORDER_STATUS_FINIHSED = "FINIHSED";//已完成               orderStatus的状态
    private static final String ORDER_STATUS_WAIT_SEND = "WAIT_FOR_SEND";//拍照订单已转换审核通过，等待发送给患者的状态
    private static final String ORDER_STATUS_CANCEL = "CANCELED";//订单状态，已取消
    private static final String ORDER_STATUS_EXPIRE = "EXPIRE";//订单已过期


    private String orderId;//订单id.
    private ConfirmDialog confirmDialog;

    private InputDialog inputDialog;
    private String doseCount;

    private RequestCallBack detailCallBack;
    private RequestCallBack lockCallBack;
    private RequestCallBack unlockCallBack;
    private RequestCallBack deleteCallBack;
    private RequestCallBack timesCallBack;
    private MedicationDetailBean mDetailBean;

   /* @IntDef({MEDICATION_EDITABLE, MEDICATION_UNEDITABLE, MEDICATION_PREVIEW})
    public static @interface MedicationDetailType {
    }*/

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionbarData();//设置actioinbar
        initEmptyView(); //初始化emptyView
        getIntentData(savedInstanceState);//获取intent的参数
        initCopyButton(); //初始化复制按钮
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(EXTRA_ORDER_ID, orderId);
    }

    //初始化emptyView
    private void initEmptyView() {
        emptyView.setVisibility(View.GONE);
        emptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
            @Override
            public void onReload() {
                detailCallBack = addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL, RequestParams.getMedicationDetailParams(orderId), MedicationDetailBean.class
                        , MedicationDetailActivity.this);
            }
        });

    }

    private void setActionbarData() {//设置actionbar
        setActionBarContentView(R.layout.activity_medication_detail);
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE);
        setActionBarTitle("用药详情");
        setActionBarRightBtnText("药品明细");
        pictureImg.setOnClickListener(this);
    }

    /**
     * 获取intent的数据
     */
    private void getIntentData(Bundle bundle) {
        if (bundle == null) {
            orderId = getIntent().getStringExtra(EXTRA_ORDER_ID);
        } else {
            orderId = bundle.getString(EXTRA_ORDER_ID);
        }
        LogUtils.i(OkHttpUtils.class, "orderId........" + orderId);
        detailCallBack = addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL, RequestParams.getMedicationDetailParams(orderId), MedicationDetailBean.class, this);

    }

    /**
     * 初始化复制按钮
     */
    private void initCopyButton() {
        btnCopyOrderNumber.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                copyOrderNumberToClipboard();
            }
        });
    }

    /**
     * 复制订单编号到粘贴板
     */
    private void copyOrderNumberToClipboard() {
        if (mDetailBean != null && !TextUtils.isEmpty(mDetailBean.getId())) {
            android.content.ClipboardManager clipboard = (android.content.ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
            android.content.ClipData clip = android.content.ClipData.newPlainText("订单编号", mDetailBean.getId());
            clipboard.setPrimaryClip(clip);
            ToastUtils.showShortMsg(this, "已复制订单编号到粘贴板");
        } else {
            ToastUtils.showShortMsg(this, "订单编号为空，无法复制");
        }
    }

    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        if (mDetailBean != null && mDetailBean.getDetails() != null) {
            List<MedicineDetailMsgBean> list = new ArrayList<>();
            BigDecimal totalPrice = new BigDecimal("0.0");
            for (MedicationDetailBean.DetailsBean detail : mDetailBean.getDetails()) {
                MedicineDetailMsgBean bean = new MedicineDetailMsgBean();
                bean.setDrugName(detail.getName());
                bean.setDose(detail.getAmount());
                bean.setUnit(detail.getUnit());
//                bean.setRecipelSalePrice(DecimalUtils.multiply(DecimalUtils.div(detail.getTotalPrice(), detail.getAmount(), 4), "0.7"));
                bean.setPrice(DecimalUtils.div(detail.getTotalPrice(), detail.getAmount(), 3));
                totalPrice = totalPrice.add(new BigDecimal(detail.getTotalPrice()));
                list.add(bean);
            }
            OrderMsgBean orderMsgBean = new OrderMsgBean();
            orderMsgBean.setPreDetailList(list);
            orderMsgBean.setDrugPrice(totalPrice.setScale(3, BigDecimal.ROUND_UP).toString());
            MedicationDrugDetailsActivity.launch(this, orderMsgBean);
        }
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (emptyView == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.SEND_PICTURE_PATIENT://发送拍照药方给患者
                if (result != null && result.isRequestSuccessed()) {
                    ToastUtils.showShortMsg(this, "发送成功");
                    setResult(Activity.RESULT_OK);
                    finish();
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.MEDICATION_DETAIL:////请求用药详情
                if (result != null && result.isRequestSuccessed()) {
                    emptyView.setVisibility(View.GONE);
                    MedicationDetailBean medicationDetailBean = (MedicationDetailBean) result.getBodyObject();
                    if (medicationDetailBean != null) {
                        setData(medicationDetailBean);
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                    emptyView.setVisibility(View.VISIBLE);
                    emptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                }
                break;
            case HttpUrlManager.MEDICATION_DETAIL_DELETE:
                if (result != null && result.isRequestSuccessed()) {//作废
                    LogUtils.i(OkHttpUtils.class, "作废成功");
                    ToastUtils.showShortMsg(this, "订单已作废");
                    closeConfirmDialog();

                    setResult(RESULT_OK);
                    finish();
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.MEDICATION_DETAIL_LOCK://加锁
                if (result != null && result.isRequestSuccessed()) {
                    isLocking = true;
                    LogUtils.i(OkHttpUtils.class, "加锁成功");
                    if (!islock_delete) {
                        //显示修改剂数的dialog
                        showModifyDoseDialog();
                    } else {//作废的加锁
                        showDeleteDialog();
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.MEDICATION_DETAIL_UNLOCK://解锁
                if (result != null && result.isRequestSuccessed()) {
                    LogUtils.i(OkHttpUtils.class, "解锁成功");
                    isLocking = false;
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.MEDICATION_TIMES://修改剂数成功
                if (result != null && result.isRequestSuccessed()) {
                    tvDoseCount.setText(createSpannable("共", doseCount, "剂"));
                    ToastUtils.showShortMsg(this, "修改成功");
                    closeInputDialog();
                    //解锁
                    unlockCallBack = addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL_UNLOCK, RequestParams.getMedicationDetailUnLock(orderId), ResponseResult.class, this);
                    //再次刷新数据
                    detailCallBack = addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL, RequestParams.getMedicationDetailParams(orderId), MedicationDetailBean.class, this);
                    LogUtils.i(OkHttpUtils.class, "修改剂数成功");
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
        }
    }

    /**
     * 设置数据
     *
     * @param medicationDetailBean
     */
    public void setData(MedicationDetailBean medicationDetailBean) {
        if (medicationDetailBean == null) {
            return;
        }
        mDetailBean = medicationDetailBean;
        
        // 设置订单编号
        if (!TextUtils.isEmpty(medicationDetailBean.getId())) {
            tvOrderNumber.setText(medicationDetailBean.getId());
        } else {
            tvOrderNumber.setText("暂无");
        }
        tvName.setText("姓名：" + medicationDetailBean.getName());
        tvAge.setText("年龄：" + medicationDetailBean.getAge());
        if (medicationDetailBean.getSex().equals("1")) {//"1"男
            tvSex.setText("性别：" + "男");
        } else {
            tvSex.setText("性别：" + "女");
        }

        //设置shi是否怀孕等
        if ("1".equals(medicationDetailBean.getIspregnant())) {//怀孕了
            tvPregant.setText("怀孕");
        } else {
            tvPregant.setText("");
        }

        //设置病症
        tvDisease.setText(medicationDetailBean.getDialectical());
        //设置药房id
        if (TextUtils.isEmpty(medicationDetailBean.getDcId())) {
            dcIdTv.setText("药房正在分配中");
        } else {
            dcIdTv.setText(medicationDetailBean.getDcId());
        }
        //设置拍方照片
        if (!TextUtils.isEmpty(medicationDetailBean.getPrescriptionUrl())) {
            pictureLinear.setVisibility(View.VISIBLE);
            mGlideUtils.loadImage(medicationDetailBean.getPrescriptionUrl(), this, pictureImg, R.drawable.icon_img_load_error);
        } else {
            pictureLinear.setVisibility(View.GONE);
        }
        //设置药方剂型一下
        String drugForm = medicationDetailBean.getDrugForm();
        SpannableStringBuilder ssbDrugForm = new SpannableStringBuilder(drugForm);
        if (!TextUtils.isEmpty(medicationDetailBean.getProductProviteName())) {//添加厂商
            ssbDrugForm.append("(");
            ssbDrugForm.append(medicationDetailBean.getProductProviteName());
            ssbDrugForm.append(")");
        }

        tvMedicineType.setText(ssbDrugForm);
        llMedicineType.setVisibility(TextUtils.isEmpty(drugForm) ? View.GONE : View.VISIBLE);

        // 显示规格信息（根据剂型判断是否显示）
        String specificationText = getSpecificationText(medicationDetailBean, drugForm);
        if (!TextUtils.isEmpty(specificationText) && shouldShowSpecification(drugForm)) {
            llSpecification.setVisibility(View.VISIBLE);
            tvSpecification.setText(specificationText);
        } else {
            llSpecification.setVisibility(View.GONE);
        }
        
        // 显示辅料信息（只有膏方剂型且有辅料数据时才显示，在规格后面显示）
        showAuxiliaryMaterial(medicationDetailBean, drugForm);

        //dnvDoseCount.setCenterContent(medicationDetailBean.getCount());
        //dnvDoseDay.setCenterContent(medicationDetailBean.getDay());
        //dnvDoseTimes.setCenterContent(medicationDetailBean.getTimes());
        //dnvDrinkTime.setCenterContent(medicationDetailBean.getEatTime());
        //用法用量的显示

        //设置修改功能
       /* llModify.setEnabled(true);
        tvModify.setTextColor(getResources().getColor(R.color.color_1a));
        ivModify.setImageResource(R.drawable.medication_modify);*/

        if (PublicParams.DOSAGEFORM_GRANULE.equals(drugForm) ||
                PublicParams.DOSAGEFORM_SLICES.equals(drugForm) ||
                PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm) ||
                PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(drugForm)) {
            //颗粒等
            tvDoseCount.setText(createSpannable("共", medicationDetailBean.getTotalCount(), "剂"));
            tvDoseDay.setText(createSpannable("每日", medicationDetailBean.getPreUsage(), "剂"));
            tvDoseTimes.setText(createSpannable("每剂分", medicationDetailBean.getPreDays(), "次服用"));
            tvDrinkTime.setText(medicationDetailBean.getEatTime() + "服用");

            tvDoseTimes.setVisibility(View.VISIBLE);
            tvDrinkTime.setVisibility(View.VISIBLE);

        } else if (PublicParams.DOSAGEFORM_POWDER.equals(drugForm) ||
                PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(drugForm)
                || PublicParams.DOSAGEFORM_WATERED_PILL.equals(drugForm) ||
                PublicParams.DOSAGEFORM_HONEYED_PILL.equals(drugForm)
            || PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(drugForm)) {
            //散剂、膏方、水丸、蜜丸、胶囊

            tvDoseCount.setText(createSpannable("每日", medicationDetailBean.getMrjc(), "次"));

            // 统一使用mcDose和mcDoseUnit显示用法用量
            if (!TextUtils.isEmpty(medicationDetailBean.getMcDose()) && !TextUtils.isEmpty(medicationDetailBean.getMcDoseUnit())) {
                // 有单位数量时，显示格式：每次 x 单位 (yg)
                try {
                    double gramAmount = Double.parseDouble(medicationDetailBean.getMcjk());
                    tvDoseDay.setText(createSpannable("每次", medicationDetailBean.getMcDose(), medicationDetailBean.getMcDoseUnit(), "(" + String.format("%.1f", gramAmount) + "g)"));
                } catch (NumberFormatException e) {
                    // 解析失败时回退到传统显示
                    tvDoseDay.setText(createSpannable("每次", medicationDetailBean.getMcjk(), "g"));
                }
            } else {
                // 没有单位数量时，显示传统格式：每次 xg
                tvDoseDay.setText(createSpannable("每次", medicationDetailBean.getMcjk(), "g"));
            }

            tvDoseTimes.setText(createSpannable("预计可用", medicationDetailBean.getEatDays(), "天"));
            tvDrinkTime.setText(createSpannable(medicationDetailBean.getEatTime(), "", "服用"));

            tvDoseTimes.setVisibility(View.VISIBLE);
            tvDrinkTime.setVisibility(View.VISIBLE);

            llModify.setVisibility(View.GONE);
            //水丸等，没有修改功能
           /* llModify.setEnabled(false);
            tvModify.setTextColor(getResources().getColor(R.color.br_color_et_hint));
            ivModify.setImageResource(R.drawable.medication_modify_uneable);*/

        }else {
            tvDoseTimes.setVisibility(View.GONE);
            tvDrinkTime.setVisibility(View.GONE);
        }

        //设置服药禁忌
        tvAvoid.setText(TextUtils.isEmpty(medicationDetailBean.getContraindication()) ? "无" : medicationDetailBean.getContraindication());
        //服药说明
        tvGuid.setText(TextUtils.isEmpty(medicationDetailBean.getRemak()) ? "无" : medicationDetailBean.getRemak());
        
//        if (PublicParams.DOSAGEFORM_SLICES.equals(drugForm) && !TextUtils.isEmpty(medicationDetailBean.getIsDaiJian())) {
        if (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm)) {
            llDaijian.setVisibility(View.VISIBLE);
            llDaiJianPreference.setVisibility(View.VISIBLE);
//            if ("1".equals(medicationDetailBean.getIsDaiJian())) {
                tvDaiJian.setText("代煎");
                tvDaiJianPreference.setText(medicationDetailBean.getMakeMethod());
//            } else if ("0".equals(medicationDetailBean.getIsDaiJian())) {
//                tvDaiJian.setText("自煎");
//            }
        } else {
            llDaijian.setVisibility(View.GONE);
            llDaiJianPreference.setVisibility(View.GONE);
        }

        String secretType = "正常处方";
        if ("1".equals(medicationDetailBean.getIsSecrecy())) {
            secretType = "保密处方";
        } else if ("2".equals(medicationDetailBean.getIsSecrecy())) {
            secretType = "剂量保密";
        }

        tvSecretTypeInput.setText(secretType);

        //用药方法
        if ((PublicParams.DOSAGEFORM_SLICES.equals(drugForm) ||
                PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm) ||
                PublicParams.DOSAGEFORM_POWDER.equals(drugForm)) &&
                (Objects.equals(medicationDetailBean.getMode(), "内服") || Objects.equals(medicationDetailBean.getMode(), "外用"))) {
            lluseDrugMethod.setVisibility(View.VISIBLE);
            tvUseMethod.setText(medicationDetailBean.getMode());
        } else  {
            lluseDrugMethod.setVisibility(View.GONE);
        }

        //设置金额
        tvMoney.setText("¥" + medicationDetailBean.getAmount());

        //设置FlowLayout的内容
        if (medicationDetailBean.getDetails() != null) {
            setFlowLayout(medicationDetailBean.getDetails(), drugForm);
        }
        //设置是否显示下边的操作按钮
        if (ORDER_NO_PAY.equals(medicationDetailBean.getPayStatus()) && ORDER_STATUS_FOR_PAY.equals(medicationDetailBean.getOrderStatus())) { //未支付
            //llDelete.setVisibility(View.VISIBLE);
            // llModify.setVisibility(View.VISIBLE);
        } else if (ORDER_STATUS_WAIT_SEND.equals(medicationDetailBean.getOrderStatus())) {
            llModify.setVisibility(View.GONE);
            llClip.setVisibility(View.GONE);
            sendBtn.setVisibility(View.VISIBLE);
        } else {
            llDelete.setVisibility(View.GONE);
            llModify.setVisibility(View.GONE);
        }

        //设置按语
        if (ORDER_PAYED.equals(medicationDetailBean.getPayStatus()) && !TextUtils.isEmpty(medicationDetailBean.getComment())) {//已支付,并且按语内容不为空
            llSecret.setVisibility(View.VISIBLE);
            tvSecret.setText(medicationDetailBean.getComment());
        } else {
            llSecret.setVisibility(View.GONE);
        }

        //药房代码的显示
        if (ORDER_PAYED.equals(medicationDetailBean.getPayStatus()) || ORDER_STATUS_FINIHSED.equals(medicationDetailBean.getOrderStatus())) {//已支付或已完成才显示药房代码
            llPharmacyCode.setVisibility(View.VISIBLE);
        } else {
            llPharmacyCode.setVisibility(View.GONE);
        }

        // 设置是否显示EmptyView
        if (TextUtils.isEmpty(medicationDetailBean.getOrderStatus())) {
            emptyView.setVisibility(View.GONE);
        } else {
            if (ORDER_STATUS_CANCEL.equals(medicationDetailBean.getOrderStatus())) {//订单已作废
                emptyView.setVisibility(View.VISIBLE);
                emptyView.setEmptyType(EmptyView.TYPE_EMPTY);
                emptyView.setEmptyText("该订单已失效");
                emptyView.setEmptyImgResource(R.drawable.order_delete);
            } else {
                emptyView.setVisibility(View.GONE);
            }
        }

    }

    public void setFlowLayout(List<MedicationDetailBean.DetailsBean> detailsBeanList, String drugForm) { //设置FlowLayout的内容
        //清除之前的数据
        flowLayout.removeAllViews();
        //添加内容
        for (int i = 0; i < detailsBeanList.size(); i++) {
            MedicationDetailBean.DetailsBean detailBean = detailsBeanList.get(i);
            TextView tvItem = new TextView(this);
            int paddingLeft = DensityUtils.dip2px(this, 15);
            int paddingTop = DensityUtils.dip2px(this, 5);
            tvItem.setPadding(paddingLeft, paddingTop, paddingLeft, paddingTop);
            tvItem.setTextSize(15);
            tvItem.setTextColor(getResources().getColor(R.color.br_color_theme_text));
            String content = detailBean.getName() + " " + detailBean.getAmount() + detailBean.getUnit();
            SpannableStringBuilder ssb = new SpannableStringBuilder(content);
            //添加  （先煎）的内容，只有内容不为空，并且为“饮片”时显示
            if (!TextUtils.isEmpty(detailBean.getType()) && (PublicParams.DOSAGEFORM_SLICES.equals(drugForm) || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm))) {
                String subContent = "(" + detailBean.getType() + ")";
                ssb.append(subContent);
                ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_red_ef4d3b)), content.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                ssb.setSpan(new AbsoluteSizeSpan(DensityUtils.sp2px(this, 12)), content.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
            tvItem.setText(ssb);
            ViewGroup.MarginLayoutParams params = new ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            //params.rightMargin = DensityUtils.dip2px(this,12);
            flowLayout.addView(tvItem, params);

        }
    }


    @Override
    @OnClick({R.id.ll_delete, R.id.ll_modify, R.id.ll_clip, R.id.send_btn, R.id.picture_img})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.picture_img://点击药方图片
                if (mDetailBean == null) {
                    addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL, RequestParams.getMedicationDetailParams(orderId), MedicationDetailBean.class, this);
                    return;
                }
                ShowImageActivity.showImage(this, Lists.<String>newArrayList(mDetailBean.getPrescriptionUrl()), mDetailBean.getPrescriptionUrl());
                break;
            case R.id.ll_delete://作废
                //先加锁
                islock_delete = true;
                lockCallBack = addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL_LOCK, RequestParams.getMedicationDetailLock(orderId), ResponseResult.class
                        , MedicationDetailActivity.this);
                break;
            case R.id.ll_modify://修改剂数
                //先加锁
                islock_delete = false;
                lockCallBack = addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL_LOCK, RequestParams.getMedicationDetailLock(orderId), ResponseResult.class
                        , MedicationDetailActivity.this);
                //showModifyDoseDialog();
                break;
            case R.id.ll_clip://复制功能
                if (mDetailBean != null) {
                    List<MedicationDetailBean.DetailsBean> details = mDetailBean.getDetails();
                    String clipContent = JSON.toJSON(details).toString();
                    LogUtils.log("clipContent............" + clipContent);
                    new ClipMedicationHelper().clipToBoard(clipContent, System.currentTimeMillis() + "");//复制功能
                    ToastUtils.showShortMsg(this, "复制成功");
                }
                break;
            case R.id.send_btn://发送拍照药方给患者
                if (mDetailBean == null) {
                    addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL, RequestParams.getMedicationDetailParams(orderId), MedicationDetailBean.class, this);
                    return;
                }
                sendToPatient(mDetailBean.getId());
                break;
            default:
                break;
        }
    }

    /**
     * 发送拍照订单给患者
     *
     * @param orderId 订单id
     */
    private void sendToPatient(String orderId) {
        Map<String, String> map = new HashMap<>();
        map.put("orderId", orderId);
        map.put("operateType", "1");//1 医生发送给患者 2 小然发送给医生
        addHttpPostRequest(HttpUrlManager.SEND_PICTURE_PATIENT, map, null, this);
    }

    private void showDeleteDialog() {
        confirmDialog = ConfirmDialog.getInstance(this).setDialogContent("是否作废此药方订单？").setNavigationText("取消")
                .setPositiveText("作废")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        closeConfirmDialog();//关闭dialog
                        requestLockData();//请求加锁的信息
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        deleteCallBack = addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL_DELETE, RequestParams.getMedicationDetailDelete(orderId)
                                , ResponseResult.class, MedicationDetailActivity.this);
                    }
                });
        confirmDialog.show();
        confirmDialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                requestLockData();
            }
        });
    }

    private void showModifyDoseDialog() {

        inputDialog = InputDialog.getInstance(this)
                .setInputTitle("修改剂数")
                .setInputHint("请输入待修改剂数")
                .setNegativeText("取消")
                .setPositiveText("保存")
                .setOnButtonClickListener(new OnButtonClickListener() {
                    @Override
                    public void onPositiveClick(View view, CharSequence editText) {
                        doseCount = editText.toString().trim();
                        if (TextUtils.isEmpty(doseCount) || 0 == Integer.parseInt(doseCount)) {   //没有修改，不请求数据
                            ToastUtils.showShortMsg(MedicationDetailActivity.this, "请输入正确剂数");
                            return;
                        }
                        
                        // 添加青庐厂商剂数验证逻辑
                        if (!validateDoseCountForSupplier(doseCount)) {
                            return; // 验证失败，不继续执行
                        }
                        
                        //修改剂数
                        timesCallBack = addHttpPostRequest(HttpUrlManager.MEDICATION_TIMES, RequestParams.getMedicationTimes(orderId, doseCount)
                                , ResponseResult.class, MedicationDetailActivity.this);
                    }

                    @Override
                    public void onNegativeClick(View view, CharSequence editText) {
                        closeInputDialog();
                        requestLockData();
                    }
                });
        inputDialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                requestLockData();
                LogUtils.log(".............我被取消了");
            }
        });
        inputDialog.setEditTextInputType(InputType.TYPE_CLASS_NUMBER);
        inputDialog.setTitle("修改剂数");
        inputDialog.show();
    }

    public void closeConfirmDialog() {
        if (confirmDialog != null) {
            confirmDialog.dismiss();
        }
    }

    public void closeInputDialog() {
        if (inputDialog != null) {
            inputDialog.dismiss();
        }
    }


    public void requestLockData() {//请求加锁的接口
        if (isLocking) {
            //解锁
            unlockCallBack = addHttpPostRequest(HttpUrlManager.MEDICATION_DETAIL_UNLOCK, RequestParams.getMedicationDetailUnLock(orderId), ResponseResult.class
                    , MedicationDetailActivity.this);
        }
    }

    /**
     * 生成，如 “共 1 剂”的样式（1为蓝色）
     *
     * @param leftContent
     * @param centerContent
     * @param rightContent
     * @return
     */
    public SpannableStringBuilder createSpannable(String leftContent, String centerContent, String rightContent) {
        SpannableStringBuilder ssb = new SpannableStringBuilder(leftContent);
        if (!TextUtils.isEmpty(centerContent)) {
            ssb.append(" " + centerContent + " ");
            ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_tab_checked))
                    , leftContent.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        if (!TextUtils.isEmpty(rightContent)) {
            ssb.append(rightContent);
        }
        return ssb;
    }

    public SpannableStringBuilder createSpannable(String leftContent, String centerContent, String rightContent, String redContent) {
        SpannableStringBuilder ssb = new SpannableStringBuilder(leftContent);
        if (!TextUtils.isEmpty(centerContent)) {
            ssb.append(" " + centerContent + " ");
            ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_tab_checked)),
                    leftContent.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        if (!TextUtils.isEmpty(rightContent)) {
            ssb.append(rightContent);
        }

        // 添加红色文字
        if (!TextUtils.isEmpty(redContent)) {
            ssb.append(" ");
            int startIndex = ssb.length();
            ssb.append(redContent);
            ssb.setSpan(new ForegroundColorSpan(Color.RED),
                    startIndex, ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }

        return ssb;
    }

    // 辅料显示相关控件（动态创建）
    private LinearLayout llAuxiliaryMaterial;
    private TextView tvAuxiliaryMaterial;

    /**
     * 显示辅料信息（只有膏方剂型且有辅料数据时才显示）
     * @param medicationDetailBean 用药详情数据
     * @param drugForm 剂型
     */
    private void showAuxiliaryMaterial(MedicationDetailBean medicationDetailBean, String drugForm) {
        // 移除之前创建的辅料显示组件（如果存在）
        if (llAuxiliaryMaterial != null && llAuxiliaryMaterial.getParent() != null) {
            ((LinearLayout) llAuxiliaryMaterial.getParent()).removeView(llAuxiliaryMaterial);
            llAuxiliaryMaterial = null;
            tvAuxiliaryMaterial = null;
        }
        
        // 只有膏方剂型且有辅料数据时才显示
        if (!PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(drugForm) || 
            medicationDetailBean == null || 
            TextUtils.isEmpty(medicationDetailBean.getMakeMaterial())) {
            return;
        }
        
        // 创建辅料显示的LinearLayout
        llAuxiliaryMaterial = new LinearLayout(this);
        llAuxiliaryMaterial.setOrientation(LinearLayout.HORIZONTAL);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        layoutParams.topMargin = DensityUtils.dip2px(this, 15);
        llAuxiliaryMaterial.setLayoutParams(layoutParams);
        
        // 创建"辅料"标签
        TextView tvLabel = new TextView(this);
        tvLabel.setText("辅　　料");
        tvLabel.setTextSize(15);
        tvLabel.setTextColor(getResources().getColor(R.color.br_color_et_hint));
        LinearLayout.LayoutParams labelParams = new LinearLayout.LayoutParams(
            DensityUtils.dip2px(this, 80), 
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        tvLabel.setLayoutParams(labelParams);
        
        // 创建辅料内容TextView
        tvAuxiliaryMaterial = new TextView(this);
        tvAuxiliaryMaterial.setText(medicationDetailBean.getMakeMaterial());
        tvAuxiliaryMaterial.setTextSize(15);
        tvAuxiliaryMaterial.setTextColor(getResources().getColor(R.color.br_color_theme_text));
        LinearLayout.LayoutParams contentParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT, 
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        contentParams.leftMargin = DensityUtils.dip2px(this, 8);
        tvAuxiliaryMaterial.setLayoutParams(contentParams);
        
        llAuxiliaryMaterial.addView(tvLabel);
        llAuxiliaryMaterial.addView(tvAuxiliaryMaterial);
        
        // 找到主容器LinearLayout，在规格后面插入辅料显示
        LinearLayout mainContainer = (LinearLayout) llSpecification.getParent();
        if (mainContainer != null) {
            // 找到规格容器在主容器中的位置
            int specificationIndex = -1;
            for (int i = 0; i < mainContainer.getChildCount(); i++) {
                if (mainContainer.getChildAt(i) == llSpecification) {
                    specificationIndex = i;
                    break;
                }
            }
            
            // 在规格后面插入辅料显示
            if (specificationIndex >= 0) {
                mainContainer.addView(llAuxiliaryMaterial, specificationIndex + 1);
            } else {
                // 如果没找到规格容器，在剂型后面插入
                int medicineTypeIndex = -1;
                for (int i = 0; i < mainContainer.getChildCount(); i++) {
                    if (mainContainer.getChildAt(i) == llMedicineType) {
                        medicineTypeIndex = i;
                        break;
                    }
                }
                if (medicineTypeIndex >= 0) {
                    mainContainer.addView(llAuxiliaryMaterial, medicineTypeIndex + 1);
                }
            }
        }
    }

    /**
     * 判断是否应该显示规格信息
     * @param drugForm 剂型
     * @return true表示应该显示规格，false表示不显示规格
     */
    private boolean shouldShowSpecification(String drugForm) {
        // 当剂型为饮片、颗粒、外用中药、代煎时，不显示规格行
        return !(PublicParams.DOSAGEFORM_SLICES.equals(drugForm) ||
                PublicParams.DOSAGEFORM_GRANULE.equals(drugForm) ||
                PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(drugForm) ||
                PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm));
    }

    /**
     * 获取规格显示文本
     * @param medicationDetailBean 用药详情数据
     * @param drugForm 剂型
     * @return 规格显示文本，如果没有规格数据则返回空字符串
     */
    private String getSpecificationText(MedicationDetailBean medicationDetailBean, String drugForm) {
        if (medicationDetailBean == null || TextUtils.isEmpty(drugForm)) {
            return "";
        }

        String specification = "";

        // 根据不同剂型获取对应的规格数据
        // 统一使用packageSpec字段获取规格信息
        specification = medicationDetailBean.getPackageSpec();

        return TextUtils.isEmpty(specification) ? "" : specification;
    }

    @Override
    protected void onDestroy() {
        if (confirmDialog != null) {
            confirmDialog.cancel();
        }
        
        // 清理动态创建的辅料显示组件
        if (llAuxiliaryMaterial != null && llAuxiliaryMaterial.getParent() != null) {
            ((LinearLayout) llAuxiliaryMaterial.getParent()).removeView(llAuxiliaryMaterial);
        }
        llAuxiliaryMaterial = null;
        tvAuxiliaryMaterial = null;
        
        cancelRequest(detailCallBack);
        cancelRequest(lockCallBack);
        cancelRequest(unlockCallBack);
        cancelRequest(timesCallBack);
        cancelRequest(deleteCallBack);
        confirmDialog = null;
        inputDialog = null;
        super.onDestroy();
    }

    /**
     * 验证厂商剂数规则
     * @param doseCount 剂数
     * @return true表示验证通过，false表示验证失败
     */
    private boolean validateDoseCountForSupplier(String doseCount) {
        if (mDetailBean == null || TextUtils.isEmpty(mDetailBean.getDrugForm())) {
            return true; // 没有数据时不进行验证
        }
        
        String drugForm = mDetailBean.getDrugForm();
        String supplierName = mDetailBean.getProductProviteName();
        
        // 只对代煎剂型进行验证
        if (!PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm)) {
            return true;
        }
        
        int intDoseCount = 0;
        try {
            intDoseCount = Integer.parseInt(doseCount);
        } catch (NumberFormatException e) {
            return true; // 解析失败时不进行验证
        }
        
        // 青庐药局【精品选货】厂商的特殊规则：5剂起煎
        if (!TextUtils.isEmpty(supplierName) && supplierName.contains("青庐药局")) {
            if (intDoseCount < 5) {
                org.newapp.ones.base.widgets.AlertDialog alertDialog = org.newapp.ones.base.widgets.AlertDialog.getInstance(this);
                alertDialog.setDialogContent(supplierName + "5剂起煎，请输入不少于5剂的数量。")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(view -> {
                            alertDialog.dismiss();
                        });
                alertDialog.show();
                return false; // 阻止提交流程
            }
        }
        // 其他代煎厂商的通用规则：3剂起煎
        else if (!TextUtils.isEmpty(supplierName)) {
            if (intDoseCount < 3) {
                org.newapp.ones.base.widgets.AlertDialog alertDialog = org.newapp.ones.base.widgets.AlertDialog.getInstance(this);
                alertDialog.setDialogContent(supplierName + "3剂起煎，请输入不少于3剂的数量。")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(view -> {
                            alertDialog.dismiss();
                        });
                alertDialog.show();
                return false; // 阻止提交流程
            }
        }
        
        return true; // 验证通过
    }
}
