<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.doctor.br.activity.manage.GetMoneyActivity">
    
    <RelativeLayout
        android:id="@+id/rl_bind_wx"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@color/color_white">
        <TextView
            android:id="@+id/tv_wx"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16dp"
            android:textColor="@color/black"
            android:text="提现至微信零钱"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"/>
        <ImageView
            android:id="@+id/iv_wx_arrow"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@drawable/arrow_bottom"
            android:layout_toRightOf="@id/tv_wx"
            android:layout_alignTop="@id/tv_wx"
            android:layout_alignBottom="@id/tv_wx" />

        <TextView
            android:id="@+id/tv_wx_bind"
            android:layout_height="match_parent"
            style="@style/text_b1_16_wrap"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="8dp"
            android:paddingHorizontal="15dp"
            android:text="绑定"
            android:gravity="center_vertical"
            android:textColor="@color/br_color_theme" />

        <View
         style="@style/line_ea_half"
            android:paddingLeft="15dp"
            android:layout_alignParentBottom="true"/>
    </RelativeLayout>


    <TextView
        android:id="@+id/tv_with_draw_max"
        style="@style/text_1d2_16_wrap"
        android:text="可提现金额：0.00元"
        android:paddingLeft="15dp"
        android:layout_height="45dp"
        android:gravity="center_vertical"
        android:paddingRight="15dp"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_marginTop="10dp"
        android:layout_centerVertical="true" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@color/color_white"
        android:paddingLeft="15dp"
        android:paddingRight="15dp">
        <TextView
            android:id="@+id/tv_tip_money"
            style="@style/text_1d2_16_wrap"
            android:text="提现金额："
            android:layout_centerVertical="true" />

        <EditText
            android:id="@+id/et_money"
            android:layout_toRightOf="@id/tv_tip_money"
            android:layout_marginLeft="8dp"
            style="@style/text_1d2_16_wrap"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:hint="输入提现金额"
            android:gravity="center_vertical|left"
            android:layout_centerVertical="true"
            android:inputType="numberDecimal"
            android:textColorHint="@color/br_color_et_hint"
            />
    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/rl_rule"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="11dp"
        android:layout_gravity="right"
        android:layout_marginRight="4dp"
        android:gravity="center_vertical">
        <ImageView
            android:id="@+id/iv_rule"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/get_money"/>

        <TextView
            android:id="@+id/tv_rule"
            style="@style/text_418_16_wrap"
            android:layout_toRightOf="@id/iv_rule"
            android:text="提现规则" />


    </RelativeLayout>

    <Button
        android:id="@+id/btn_get_money"
        style="@style/btn_submit"
        android:layout_marginTop="130dp"
        android:text="提 现" />

</LinearLayout>
