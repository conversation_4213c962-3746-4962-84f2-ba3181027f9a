<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 背景遮罩层 -->
    <View
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#55000000" />

    <!-- 内容区域 -->
    <LinearLayout
        android:id="@+id/content_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginTop="0dp"
        android:background="@color/br_color_white"
        android:gravity="bottom"
        android:orientation="vertical">

        <com.doctor.br.view.MaxHeightLinearlayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/br_color_white"
            android:gravity="top"
            android:orientation="vertical"
            app:percent="0.75">

            <!-- 头部标题栏 -->
            <RelativeLayout
                android:id="@+id/rl_title"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/color_f6"
                android:paddingHorizontal="10dp">

                <ImageView
                    android:id="@+id/close_img"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_centerVertical="true"
                    android:padding="8dp"
                    android:src="@drawable/close_x" />

                <TextView
                    android:id="@+id/title_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="请选择辅料"
                    android:textColor="@color/br_color_theme_text"
                    android:textSize="@dimen/textsize_20" />

            </RelativeLayout>

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="@dimen/br_line_height"
                android:background="@color/br_color_inner_line" />

            <!-- 辅料选择区域 -->
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/br_margin_15"
                android:layout_marginBottom="@dimen/br_margin_15"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/br_margin_15">

                    <LinearLayout
                        android:id="@+id/material_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:orientation="vertical" />

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/br_line_height"
                        android:layout_marginBottom="15dp"
                        android:background="@color/br_color_inner_line" />

                    <!-- 不添加辅料按钮容器 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="start">
                        
                        <TextView
                            android:id="@+id/no_material_btn"
                            android:layout_width="wrap_content"
                            android:layout_height="40dp"
                            android:text="不添加辅料"
                            android:textColor="@color/auxiliary_material_text_color"
                            android:textSize="@dimen/textsize_15"
                            android:background="@drawable/auxiliary_material_button_selector"
                            android:gravity="center"
                            android:paddingHorizontal="15dp"
                            android:paddingVertical="10dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:layout_marginHorizontal="15dp"
                            android:layout_marginVertical="7.5dp" />
                    </LinearLayout>

                    <!-- 隐藏原来的CheckBox -->
                    <CheckBox
                        android:id="@+id/no_material_cb"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="不添加辅料"
                        android:textColor="@color/br_color_theme_text"
                        android:textSize="@dimen/textsize_15"
                        android:visibility="gone" />

                </LinearLayout>

            </ScrollView>

        </com.doctor.br.view.MaxHeightLinearlayout>

        <!-- 确定按钮 -->
        <LinearLayout
            android:id="@+id/bottom_view"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/br_color_theme"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/confirm_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="确定"
                android:textColor="@color/br_color_white"
                android:textSize="@dimen/textsize_17"
                android:clickable="true"
                android:focusable="true"
                android:background="?android:attr/selectableItemBackground" />
        </LinearLayout>

    </LinearLayout>

</FrameLayout>