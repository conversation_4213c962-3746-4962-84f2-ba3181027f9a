<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <SurfaceView
        android:id="@+id/capture_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:id="@+id/capture_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/capture_crop_view"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_centerInParent="true"
            android:background="@drawable/qr_code_bg">

            <ImageView
                android:id="@+id/capture_scan_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_marginBottom="5dp"
                android:layout_marginTop="5dp"
                android:src="@drawable/scan_line" />
        </RelativeLayout>

        <!-- 扫描提示文字 -->
        <TextView
            android:id="@+id/tv_scan_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/capture_crop_view"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="24dp"
            android:text="将取景框对准二维码，即可自动扫描"
            android:textColor="#FFFFFFFF"
            android:textSize="14sp"
            android:gravity="center" />

        <ImageView
            android:id="@+id/top_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/capture_crop_view"
            android:background="@drawable/shadow" />

        <ImageView
            android:id="@+id/bottom_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/tv_scan_hint"
            android:background="@drawable/shadow" />

        <ImageView
            android:id="@+id/left_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/bottom_img"
            android:layout_below="@id/top_img"
            android:layout_toLeftOf="@id/capture_crop_view"
            android:background="@drawable/shadow" />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/bottom_img"
            android:layout_below="@id/top_img"
            android:layout_toRightOf="@id/capture_crop_view"
            android:background="@drawable/shadow" />

        <!-- 扫码框上方的提示文案（默认不显示，仅从医案导出进入时显示） -->
        <LinearLayout
            android:id="@+id/ll_export_hint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/capture_crop_view"
            android:layout_marginLeft="24dp"
            android:layout_marginRight="24dp"
            android:layout_marginBottom="12dp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_export_hint_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请在电脑浏览器打开"
                android:textColor="#FFFFFFFF"
                android:textSize="14sp" />

            <!-- URL地址行，居中显示，右侧带复制链接 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_export_hint_url"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#FFFFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_copy_link"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:text="复制"
                    android:textColor="#4A90E2"
                    android:textSize="14sp"
                    android:background="?android:attr/selectableItemBackground"
                    android:padding="8dp"
                    android:clickable="true"
                    android:focusable="true" />
            </LinearLayout>
        </LinearLayout>
        <ImageView
            android:id="@+id/picture_img"
            android:layout_width="@dimen/br_item_height_50"
            android:layout_height="@dimen/br_item_height_50"
            android:layout_alignParentBottom="true"
            android:src="@drawable/icon_picture"
            android:padding="5dp"
            android:layout_margin="20dp"/>
    </RelativeLayout>
</RelativeLayout>