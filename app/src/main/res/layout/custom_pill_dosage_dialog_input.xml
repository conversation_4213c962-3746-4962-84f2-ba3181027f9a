<?xml version="1.0" encoding="utf-8"?>
<!-- 用于水丸、蜜丸、膏方的自定义用量弹窗布局，支持小数输入 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@color/transparent"
    android:paddingLeft="@dimen/card_margin_20"
    android:paddingRight="@dimen/card_margin_20">

    <LinearLayout
        android:layout_width="314dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/dialog_bg"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/card_margin_15"
            android:layout_marginRight="@dimen/card_margin_15"
            android:layout_marginTop="23dp"
            android:gravity="center"
            android:text="自定义用量"
            android:textColor="@color/color_theme"
            android:textSize="18sp"
            android:visibility="visible" />

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/color_line"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="20dp"
            android:layout_marginLeft="25dp"
            android:layout_marginRight="25dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:text="每次"
                    android:textSize="15dp"
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <org.newapp.ones.base.widgets.NoEmojiEditText
                    android:id="@+id/et_reply"
                    android:layout_width="80dp"
                    android:layout_height="40dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_marginTop="0dp"
                    android:background="@drawable/corner_f6f6f6_5"
                    android:gravity="center"
                    android:hint=""
                    android:inputType="numberDecimal"
                    android:maxLength="4"
                    android:paddingLeft="@dimen/br_margin_20"
                    android:paddingRight="@dimen/br_margin_20"
                    android:singleLine="true"
                    android:textColor="@color/br_color_theme_text"
                    android:textColorHint="@color/br_color_et_hint"
                    android:textSize="@dimen/textsize_16" />

                <TextView
                    android:id="@+id/tv_unit"
                    android:text="丸"
                    android:textSize="15dp"
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/tv_weight"
                    android:text="(0g)"
                    android:textSize="14dp"
                    android:textColor="@color/br_color_red_ef4d3b"
                    android:gravity="center_vertical"
                    android:layout_marginLeft="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

            </LinearLayout>

        </LinearLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/color_line"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginBottom="37dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="49dp"
            android:background="@color/transparent"
            android:orientation="horizontal"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <Button
                android:id="@+id/btn_cancel"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="118dp"
                android:layout_height="40dp"
                android:layout_marginRight="15dp"
                android:layout_weight="1"
                android:background="@drawable/bg_button_gray"
                android:text="取消"
                android:textColor="@color/color_text_title"
                android:textSize="@dimen/textsize_15" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/color_line"
                android:visibility="gone" />

            <Button
                android:id="@+id/btn_save"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="118dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/btn_round_gray"
                android:text="保存"
                android:textColor="@color/color_white"
                android:enabled="false"
                android:textSize="@dimen/textsize_15" />

        </LinearLayout>
    </LinearLayout>
</FrameLayout>