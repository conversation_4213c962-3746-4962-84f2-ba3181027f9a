<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.doctor.yy">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <uses-feature android:name="android.hardware.camera.AUTOFOCUS" /> <!-- DownloadManager下载时不显示Notification -->
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" /> <!-- 读取设置权限 -->
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" /> <!-- 创建和删除快捷方式权限 -->
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> <!-- 添加华为角标权限 -->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" /> <!-- <uses-permission android:name="android.permission.BROADCAST_SMS" /> &lt;!&ndash; ShareSDK添加必要的权限 &ndash;&gt; -->
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- // 在应用的AndroidManifest.xml添加如下<queries>标签 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <queries>
        <package android:name="com.tencent.mm" />
        <!-- // 指定微信包名 -->
    </queries>

    <!-- 移除 MobSDK/ShareSDK 自动引入的短信权限 -->
    <uses-permission
        android:name="android.permission.RECEIVE_SMS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_SMS"
        tools:node="remove" />
    
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_CALL_LOG"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.SET_DEBUG_APP"
        tools:ignore="ProtectedPermissions"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.GET_ACCOUNTS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.USE_CREDENTIALS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.MANAGE_ACCOUNTS"
        tools:node="remove" />
    <application
        android:name="com.doctor.br.app.AppContext"
        android:allowBackup="true"
        android:icon="@mipmap/logo"
        android:label="@string/app_name"
        android:persistent="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:theme,android:name">

        <activity
            android:name="com.doctor.br.activity.medical.QuicklyPrescriptionActivity"
            android:exported="false" />
            
        <activity
            android:name="com.doctor.br.activity.medical.QuickPrescriptionFragmentActivity"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.doctor.yy.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"
                tools:replace="android:name,android:resource" />
        </provider>

        <meta-data
            android:name="com.doctor.br.utils.glide.SimpleGlideModule"
            android:value="GlideModule"
            tools:node="remove" /> <!-- 极光推送 -->
        <!-- 极光推送 -->
        <activity
            android:name="com.doctor.br.activity.LauncherActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode"
            android:theme="@style/LauncherTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.doctor.br.activity.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode"
            android:screenOrientation="portrait">

            <!-- <intent-filter> -->
            <!-- <action android:name="android.intent.action.MAIN" /> -->


            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
            <!-- </intent-filter> -->
        </activity>
        <activity
            android:name="com.doctor.br.activity.ShowImageActivity"
            android:theme="@style/LauncherTheme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.doctor.br.activity.LoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.LoadDataActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.RegisterActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.photo.ClipImageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.ChatMainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.doctor.br.activity.chatmain.ChatServiceActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.doctor.br.activity.QrCodeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.zxing.activity.CaptureActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.ChatQrScanActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.SessionSearchActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.ContactsSearchActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="com.doctor.br.activity.manage.MyWalletActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.AboutActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.ProblemActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.doctor.br.activity.mine.QualificationingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.UsingHelpActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.AgentActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.DownloadActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.task.SelectAgentActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.framework.FrameworkSearchActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.framework.FrameworkDispatchActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.framework.FrameworkActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.task.SelectAgentSearchActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.DispatchActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.PendingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.doctorState.AgentQualificationedActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.exceptionDoctor.TwoWeekNoOrderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.exceptionDoctor.FiveOrderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.DoctorStateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.doctorState.NotRegisterActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.doctorState.NotQualificationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.doctorState.AgentQualificationingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.doctorState.AgentQualificationFailedActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.doctorState.NoPatientActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.doctorState.NoOrderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.doctorState.NoPayActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.ExceptionDoctorActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.DoctorDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.AchievementActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.achievement.PatientNumbersActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.achievement.PrescriptionNumbersActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.achievement.RatioIncomeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.achievement.PerformanceSummaryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.achievement.AverageUnitPriceActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.achievement.OrderPriceActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.ProtocolActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.ForgetPasswordActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.medical.AddMedicineActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity android:name="com.doctor.br.activity.medical.BRIntelligentEntryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.PharmacopoeiaQueryActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.doctor.br.activity.manage.MedicineDetailsActivity"
            android:screenOrientation="portrait" /> <!-- <activity android:name="com.doctor.br.activity.manage.GroupMsgActivity" /> -->
        <activity
            android:name="com.doctor.br.activity.manage.ServiceSettingActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.doctor.br.activity.manage.MyNoticeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.MedicationDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.VisitingSettingsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.BlackListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.QualificationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.SafeSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.ModifyPhoneActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name="com.doctor.br.activity.mine.ModifyWithdrawPasswordActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name="com.doctor.br.activity.mine.PersonalDataEditTextActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible|adjustResize" />

        <service
            android:name="com.doctor.br.service.NettyMessageService"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.doctor.br.activity.dialog.ConflictDialogActivity"
            android:screenOrientation="portrait"
            android:theme="@style/style_ConflictDialogActivity" />
        <activity
            android:name="com.doctor.br.activity.dialog.UpdateDownloadActivity"
            android:screenOrientation="portrait"
            android:theme="@style/style_ConflictDialogActivity" />
        <activity android:name="com.doctor.br.activity.medical.CustomBoilyWayActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.dialog.ActiveListDialogActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/style_ConflictDialogActivity" />

        <receiver android:name="com.doctor.br.receiver.DownloadManagerReceiver">
            <intent-filter>

                <!-- 配置 点击通知 和 下载完成 两个 action -->
                <action android:name="android.intent.action.DOWNLOAD_NOTIFICATION_CLICKED" />
                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.doctor.br.receiver.JPushReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.RECEIVE_MESSAGE" />
                <category android:name="com.doctor.yy" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.doctor.br.activity.mine.PersonalDataActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.OrderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.ActionBarWebViewActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.NoActionBarWebViewActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.medical.AddPatientActivity"
            android:theme="@style/NoTitleTranslucentTheme" />
        <activity
            android:name="com.doctor.br.activity.AddAndEditVisitingMsgActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.ChatHistoryRecordsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.PatientInfoActivity"
            android:screenOrientation="portrait" /> <!-- sharesdk分享使用 -->
        <activity
            android:name="com.mob.tools.MobUIShell"
            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
            android:multiprocess="true"
            android:screenOrientation="portrait"
            android:theme="@style/NoTitleTranslucentTheme"
            android:windowSoftInputMode="stateHidden|adjustResize" />
        <!--
           sharesdk
           如果集成QQ分享，或者使用QQ客户端来进行QQ空间的分享，须要在此处添加一个回调activity，
           对ACTION_VIEW事件的过滤器，其中的scheme是"tencent"前缀再开发者应用的加上appId。如
           果此过滤器不设置，则分享结束以后不能得到正确的回调
        -->
        <activity
            android:name="cn.sharesdk.tencent.qq.ReceiveActivity"
            android:launchMode="singleTask"
            android:noHistory="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- tencent后的数是AppId -->
                <!-- 测试环境 -->
                <!-- <data android:scheme="tencent1106618516"/> -->
                <!-- 正式环境 -->
                <data android:scheme="tencent100424468" />
            </intent-filter>
        </activity> <!-- 微信分享回调 -->
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|uiMode"
            android:exported="true"
            android:theme="@style/NoTitleTranslucentTheme" />
        <activity
            android:name="com.doctor.br.activity.dialog.NoticeDialogActivity"
            android:screenOrientation="portrait"
            android:theme="@style/style_ConflictDialogActivity" />
        <activity
            android:name="com.doctor.br.activity.manage.CommonPrescriptionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.SearchPrescriptionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.ChooseContactActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.ChooseCommonPrescriptionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.SystemMsgListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.task.TaskActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.CertificatExampleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.CertificatExampleDetailActivity"
            android:theme="@style/NoTitleTranslucentTheme" />
        <activity
            android:name="com.doctor.br.activity.mine.QualificationSuccessActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.GetMoneyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.WxWithDrawActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.UpdateCommonProblemActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.CommonProblemEditTextActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateVisible" />
        <activity
            android:name="com.doctor.br.activity.manage.GetMoneyRuleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.BindCardActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.BankCardListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.MsgRepeatActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.AddCommonPrescriptionActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateVisible" />
        <activity
            android:name="com.doctor.br.activity.medical.PreviewMedicationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.DebugModelConfigActivity"
            android:screenOrientation="portrait"
            android:theme="@style/style_ConflictDialogActivity" />
        <activity
            android:name="com.doctor.br.activity.OnePiexlActivity"
            android:screenOrientation="portrait"
            android:theme="@style/OnePixelActivity" />
        <activity
            android:name="com.doctor.br.activity.SplashGuidActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.SearchDownloadActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.SearchClassicPrescriptionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.SecondClassicPrescriptionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.VideoPlayActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.achievement.PerformanceSearchActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.achievement.PerformanceMonthActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.agent.achievement.PerformanceEveryMonthActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.medical.PictureMedicineActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.UserProtocolActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.CameraPrescriptionOrderActitity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.CameraOrderStateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.OrderRecordActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.OrderDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.MyMedicineProtocolActivity"
            android:screenOrientation="portrait" />
        <activity android:name="com.doctor.test.TestActivity" />
        <activity
            android:name="com.doctor.br.activity.manage.medicineshop.MyMedicineShopActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.PrivacyPolicyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.mine.DeleteAccountActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.ComplaintActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.manage.WxGetMoneyRuleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.PermissionActivity"
            android:theme="@style/NoTitleTranslucentTheme" />
        <activity
            android:name="com.doctor.br.activity.medical.MedicationDrugDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.chatmain.NewChatHistoryRecordsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.doctor.br.activity.BaiTaiActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar.FullScreen" />

        <!-- 阿里云号码认证Activity -->
        <activity
            android:name="com.mobile.auth.gatewayauth.LoginAuthActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|uiMode"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@style/authsdk_activity_dialog" />

        <!-- 声明自定义的 JPush 服务 -->
        <service
            android:name="com.doctor.br.service.BRJPushService"
            android:permission="cn.jpush.android.permission.PUSH_MESSAGE"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.service.JCommonService" />
            </intent-filter>
        </service>

        <!-- 确保包含 JPush 的广播接收器 -->
        <receiver
            android:name="cn.jpush.android.service.PushReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.REGISTRATION" />
                <action android:name="cn.jpush.android.intent.MESSAGE_RECEIVED" />
                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED" />
                <action android:name="cn.jpush.android.intent.NOTIFICATION_OPENED" />
                <!-- 其他必要的动作 -->
            </intent-filter>
        </receiver>

    </application>

</manifest>